# userdata表字段映射说明

## 📋 MySQL表结构

根据您提供的现有MySQL表 `userdata` 结构：

```sql
userdata (
  id INT,
  username VARCHAR(256),
  公司 VARCHAR(256),
  投递链接 VARCHAR(256),
  重视度 INT,
  职位 VARCHAR(256),
  地点 VARCHAR(256),
  进展 VARCHAR(256),
  状态 VARCHAR(256),
  进展时间 VARCHAR(256),
  投递时间 VARCHAR(256),
  备注 VARCHAR(256),
  内推码 VARCHAR(256)
)
```

## 🔄 字段映射关系

### 前端 ↔ MySQL 字段映射

| 前端字段 (JobApplicationTable.vue) | MySQL字段 (userdata表) | 类型 | 说明 |
|-----------------------------------|------------------------|------|------|
| `id` | `id` | INT | 记录ID，主键 |
| `username` | `username` | VARCHAR(256) | 用户名 |
| `company` | `公司` | VARCHAR(256) | 公司名称 |
| `applicationLink` | `投递链接` | VARCHAR(256) | 投递链接 |
| `priority` | `重视度` | INT | 重视度/优先级 |
| `position` | `职位` | VARCHAR(256) | 职位名称 |
| `location` | `地点` | VARCHAR(256) | 工作地点 |
| `progress` | `进展` | VARCHAR(256) | 进展状态 |
| `status` | `状态` | VARCHAR(256) | 申请状态 |
| `progressDate` | `进展时间` | VARCHAR(256) | 进展时间 |
| `applicationDate` | `投递时间` | VARCHAR(256) | 投递时间 |
| `notes` | `备注` | VARCHAR(256) | 备注信息 |
| `referralCode` | `内推码` | VARCHAR(256) | 内推码 |

### 不匹配的字段

| 前端字段 | 处理方式 | 说明 |
|---------|---------|------|
| `industry` | 设为空字符串 | MySQL表中没有行业字段 |
| `tags` | 设为空字符串 | MySQL表中没有标签字段 |
| `userId` | 从本地用户表获取 | MySQL表中没有userId字段 |
| `createdAt` | 不同步 | MySQL表中没有创建时间字段 |
| `updatedAt` | 不同步 | MySQL表中没有更新时间字段 |

## 🔄 数据转换逻辑

### 1. 本地数据 → MySQL数据 (上传同步)

```javascript
// 在 mysqlSyncService.ts 中
const mysqlApplications = localApplications.map(app => ({
  username: username,
  公司: app.company || '',
  投递链接: app.applicationLink || '',
  重视度: app.priority || 2,
  职位: app.position || '',
  地点: app.location || '',
  进展: app.progress || '',
  状态: app.status || '',
  进展时间: app.progressDate || '',
  投递时间: app.applicationDate || '',
  备注: app.notes || '',
  内推码: app.referralCode || ''
}))
```

### 2. MySQL数据 → 本地数据 (下载同步)

```javascript
// 在 mysqlSyncService.ts 中
const localApplications = mysqlApplications.map(app => ({
  userId: localUser.id,
  username: username,
  company: app.公司,
  applicationLink: app.投递链接,
  priority: app.重视度,
  industry: '', // MySQL表中没有，设为空
  tags: '', // MySQL表中没有，设为空
  position: app.职位,
  location: app.地点,
  progress: app.进展,
  status: app.状态,
  progressDate: app.进展时间,
  applicationDate: app.投递时间,
  notes: app.备注,
  referralCode: app.内推码
}))
```

## 🚀 同步流程

### 普通用户操作流程

1. **输入用户名**: 在用户名输入框输入用户名
2. **点击同步**: 点击"一键同步"按钮
3. **从MySQL获取数据**: 系统查询 `userdata` 表中匹配该用户名的所有记录
4. **转换并保存到本地**: 将MySQL数据转换为本地格式并保存到IndexedDB
5. **界面显示**: 求职申请表显示从本地数据库加载的数据

### 数据修改同步流程

1. **用户修改数据**: 在求职申请表中添加、编辑或删除记录
2. **保存到本地**: 数据首先保存到本地IndexedDB数据库
3. **自动同步到MySQL**: 系统自动将修改后的数据同步到MySQL的 `userdata` 表
4. **字段转换**: 本地字段名转换为MySQL的中文字段名

## 📝 SQL操作示例

### 查询用户数据
```sql
-- 获取指定用户的所有求职申请
SELECT * FROM userdata 
WHERE username = '张三' AND 公司 != '' 
ORDER BY id DESC;

-- 获取所有用户列表
SELECT DISTINCT username FROM userdata 
WHERE username IS NOT NULL AND username != ''
ORDER BY username;
```

### 插入新记录
```sql
INSERT INTO userdata (
  username, 公司, 投递链接, 重视度, 职位, 地点, 进展, 状态, 进展时间, 投递时间, 备注, 内推码
) VALUES (
  '张三', '腾讯', 'https://careers.tencent.com/apply/123', 1, 
  '前端工程师', '深圳', '面试中', '进行中', '2024-01-15', '2024-01-10', 
  '技术面试表现良好', 'TC2024001'
);
```

### 批量更新用户数据
```sql
-- 先删除该用户的现有数据（保留占位记录）
DELETE FROM userdata WHERE username = '张三' AND 公司 != '';

-- 然后批量插入新数据
INSERT INTO userdata (username, 公司, 投递链接, 重视度, 职位, 地点, 进展, 状态, 进展时间, 投递时间, 备注, 内推码) VALUES
('张三', '腾讯', 'https://careers.tencent.com/apply/123', 1, '前端工程师', '深圳', '面试中', '进行中', '2024-01-15', '2024-01-10', '技术面试表现良好', 'TC2024001'),
('张三', '阿里巴巴', 'https://job.alibaba.com/apply/456', 1, '高级前端工程师', '杭州', '已投递', '待回复', '', '2024-01-12', '通过内推投递', 'ALI2024002');
```

## ⚠️ 注意事项

### 1. 字段长度限制
- 所有VARCHAR字段都是256字符限制
- 确保数据不超过字段长度限制

### 2. 中文字段名
- MySQL表使用中文字段名
- 代码中需要正确处理中文字段名的引用

### 3. 数据类型转换
- `重视度` 字段是INT类型，确保传入数字
- 时间字段是VARCHAR类型，以字符串形式存储

### 4. 占位记录处理
- 创建用户时会插入一条空记录（公司字段为空）
- 查询时需要过滤掉这些占位记录：`WHERE 公司 != ''`

### 5. 数据一致性
- 本地数据库作为主要数据源
- MySQL作为远程备份和多设备同步
- 修改操作先更新本地，再同步到MySQL

## 🔧 代码修改要点

### 1. 删除了表创建功能
- 移除了 `createUserTable()` 和 `createJobApplicationTable()` 方法
- 删除了管理员面板中的"创建数据库表"按钮
- 改为检查现有表是否存在

### 2. 适配现有表结构
- 更新了 `MySQLJobApplication` 接口以匹配中文字段名
- 修改了所有SQL查询以使用正确的表名和字段名
- 更新了数据转换逻辑

### 3. 简化了同步逻辑
- 直接使用现有的 `userdata` 表
- 不需要维护用户表和申请表的关联关系
- 简化了用户管理逻辑

现在系统完全适配您现有的MySQL `userdata` 表结构，可以正常进行数据同步！🎯
