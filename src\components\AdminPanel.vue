<template>
  <div class="admin-panel">
    <div class="admin-header">
      <h3>🔧 管理员配置面板</h3>
      <div class="admin-actions">
        <t-button theme="default" size="small" @click="$emit('exit-admin')">
          退出管理员模式
        </t-button>
      </div>
    </div>

    <!-- 数据源选择 -->
    <div class="admin-section">
      <div class="section-header">
        <h4>📊 数据源选择</h4>
        <p class="section-description">选择程序获取数据的方式</p>
      </div>
      
      <div class="data-source-selector">
        <t-radio-group v-model="selectedDataSource" @change="onDataSourceChange">
          <t-radio value="mysql">
            <div class="radio-option">
              <div class="radio-title">🗄️ MySQL数据库</div>
              <div class="radio-desc">从MySQL数据库获取数据（推荐）</div>
            </div>
          </t-radio>
          <t-radio value="feishu">
            <div class="radio-option">
              <div class="radio-title">📊 飞书多维表格</div>
              <div class="radio-desc">从飞书多维表格API获取数据</div>
            </div>
          </t-radio>
        </t-radio-group>
      </div>
    </div>

    <!-- MySQL配置 -->
    <div class="admin-section" v-show="selectedDataSource === 'mysql'">
      <div class="section-header">
        <h4>🗄️ MySQL数据库配置</h4>
        <p class="section-description">配置MySQL数据库连接信息</p>
      </div>

      <div class="config-grid">
        <div class="form-group">
          <label>数据库主机:</label>
          <t-input
            v-model="mysqlConfig.host"
            placeholder="mysql5.sqlpub.com"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>端口:</label>
          <t-input
            v-model.number="mysqlConfig.port"
            type="number"
            placeholder="3310"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>用户名:</label>
          <t-input
            v-model="mysqlConfig.user"
            placeholder="fish2609"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>密码:</label>
          <t-input
            v-model="mysqlConfig.password"
            type="password"
            placeholder="数据库密码"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>数据库名:</label>
          <t-input
            v-model="mysqlConfig.database"
            placeholder="jobfish_pool"
            class="config-input"
          />
        </div>
      </div>

      <div class="form-actions">
        <t-button theme="primary" @click="saveMySQLConfig">
          保存MySQL配置
        </t-button>
        <t-button theme="default" @click="testMySQLConnection">
          测试MySQL连接
        </t-button>
        <t-button theme="warning" variant="outline" @click="resetMySQLToDefault">
          恢复默认配置
        </t-button>
      </div>
    </div>

    <!-- 飞书配置 -->
    <div class="admin-section" v-show="selectedDataSource === 'feishu'">
      <div class="section-header">
        <h4>📊 飞书多维表格配置</h4>
        <p class="section-description">配置飞书多维表格连接信息</p>
      </div>

      <div class="config-grid">
        <div class="form-group">
          <label>App Token:</label>
          <t-input
            v-model="feishuConfig.appToken"
            placeholder="RR9cb8uqKaBUOGso9WTcBiXjnfh"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>热门秋招汇总 Table ID:</label>
          <t-input
            v-model="feishuConfig.tableId"
            placeholder="tbl1arQX7A2jiLh4"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>热门实习汇总 Table ID:</label>
          <t-input
            v-model="feishuConfig.internshipTableId"
            placeholder="tbl2tPFOPenoIQdg"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>国企央企汇总 Table ID:</label>
          <t-input
            v-model="feishuConfig.stateOwnedTableId"
            placeholder="tbl1UfSuMtFZ8GR9"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>App ID:</label>
          <t-input
            v-model="feishuConfig.appId"
            placeholder="cli_a80ce54e0c78100c"
            class="config-input"
          />
        </div>

        <div class="form-group">
          <label>App Secret:</label>
          <t-input
            v-model="feishuConfig.appSecret"
            type="password"
            placeholder="6l0TOvHiepUuHD5VyxmRwcXHLoRalwfQ"
            class="config-input"
          />
        </div>
      </div>

      <div class="form-actions">
        <t-button theme="primary" @click="saveFeishuConfig">
          保存飞书配置
        </t-button>
        <t-button theme="default" @click="testFeishuConnection">
          测试飞书连接
        </t-button>
        <t-button theme="warning" variant="outline" @click="resetFeishuToDefault">
          恢复默认配置
        </t-button>
      </div>
    </div>

    <!-- MySQL数据库管理 -->
    <div v-if="selectedDataSource === 'mysql'" class="admin-section">
      <h3>数据库管理</h3>
      <div class="database-actions">

        <t-button
          theme="warning"
          variant="outline"
          :loading="isTestingConnection"
          @click="testMySQLConnection"
        >
          测试连接
        </t-button>
      </div>
      <div v-if="connectionStatus" class="connection-status">
        <p :class="connectionStatus.success ? 'success' : 'error'">
          {{ connectionStatus.message }}
        </p>
      </div>
    </div>

    <!-- 用户管理 -->
    <div class="admin-section">
      <h3>用户管理</h3>
      <div class="user-management">
        <div class="user-list">
          <h4>现有用户 ({{ users.length }})</h4>
          <div v-if="users.length === 0" class="no-users">
            暂无用户
          </div>
          <div v-else class="user-items">
            <div
              v-for="user in users"
              :key="user.id"
              class="user-item"
              :class="{ active: user.isActive }"
            >
              <span class="username">{{ user.username }}</span>
              <span class="user-info">
                创建于: {{ formatDate(user.createdAt) }}
              </span>
              <div class="user-actions">
                <t-button
                  v-if="!user.isActive"
                  size="small"
                  theme="primary"
                  variant="text"
                  @click="setActiveUser(user)"
                >
                  设为当前
                </t-button>
                <t-button
                  size="small"
                  theme="danger"
                  variant="text"
                  @click="deleteUser(user.id!)"
                  :disabled="users.length <= 1"
                >
                  删除
                </t-button>
              </div>
            </div>
          </div>
        </div>

        <div class="create-user-section">
          <h4>创建新用户</h4>
          <div class="create-user-form">
            <t-input
              v-model="newUserName"
              placeholder="输入用户名"
              :status="userNameValidation.valid ? 'default' : 'error'"
              :tips="userNameValidation.message"
              @input="validateUserName"
            />
            <t-button
              theme="primary"
              :disabled="!userNameValidation.valid || !newUserName.trim()"
              @click="createUser"
            >
              创建用户
            </t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用配置按钮 -->
    <div class="admin-footer">
      <t-button theme="success" @click="applyAndExit">
        应用配置并退出
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  Input as TInput,
  Button as TButton,
  Radio as TRadio,
  RadioGroup as TRadioGroup,
  MessagePlugin
} from 'tdesign-vue-next'
import { useDataSourceStore, type DataSourceType } from '../stores/dataSourceStore'
import {
  createMySQLClient,
  validateMySQLConfig,
  loadMySQLConfigFromStorage,
  saveMySQLConfigToStorage,
  defaultMySQLConfig,
  type MySQLConfig
} from '../services/mysqlApi'
import {
  createFeishuClient,
  validateConfig,
  loadConfigFromStorage,
  saveConfigToStorage,
  type FeishuConfig
} from '../services/feishuApi'
import { userService, type LocalUser } from '../services/userService'

// 定义事件
defineEmits<{
  'exit-admin': []
}>()

// Store
const dataSourceStore = useDataSourceStore()

// 数据源选择
const selectedDataSource = ref<DataSourceType>(dataSourceStore.currentDataSource)

// MySQL配置
const mysqlConfig = ref<MySQLConfig>({
  host: '',
  port: 3310,
  user: '',
  password: '',
  database: ''
})

// 飞书配置
const feishuConfig = ref<FeishuConfig>({
  appToken: '',
  tableId: '',
  internshipTableId: '',
  stateOwnedTableId: '',
  appId: '',
  appSecret: ''
})

// 数据库管理状态
const isTestingConnection = ref(false)
const connectionStatus = ref<{ success: boolean; message: string } | null>(null)

// 用户管理状态
const users = ref<LocalUser[]>([])
const newUserName = ref('')
const userNameValidation = ref({ valid: false, message: '' })

// 数据源切换
const onDataSourceChange = (value: any) => {
  if (typeof value === 'string' && (value === 'mysql' || value === 'feishu')) {
    selectedDataSource.value = value as DataSourceType
  }
}

// MySQL相关方法
const saveMySQLConfig = async () => {
  try {
    if (!validateMySQLConfig(mysqlConfig.value)) {
      MessagePlugin.warning('请完善所有MySQL配置项')
      return
    }

    saveMySQLConfigToStorage(mysqlConfig.value)
    MessagePlugin.success('MySQL配置保存成功')
  } catch (error) {
    console.error('保存MySQL配置失败:', error)
    MessagePlugin.error('保存MySQL配置失败')
  }
}



const resetMySQLToDefault = () => {
  mysqlConfig.value = { ...defaultMySQLConfig }
  MessagePlugin.info('已恢复MySQL默认配置')
}

// 飞书相关方法
const saveFeishuConfig = async () => {
  try {
    if (!validateConfig(feishuConfig.value)) {
      MessagePlugin.warning('请完善所有飞书配置项')
      return
    }

    saveConfigToStorage(feishuConfig.value)
    MessagePlugin.success('飞书配置保存成功')
  } catch (error) {
    console.error('保存飞书配置失败:', error)
    MessagePlugin.error('保存飞书配置失败')
  }
}

const testFeishuConnection = async () => {
  try {
    if (!validateConfig(feishuConfig.value)) {
      MessagePlugin.warning('请先完善飞书配置')
      return
    }

    MessagePlugin.info('正在测试飞书连接...')
    const client = createFeishuClient(feishuConfig.value)
    const success = await client.testConnection()

    if (success) {
      MessagePlugin.success('飞书连接测试成功')
    } else {
      MessagePlugin.error('飞书连接测试失败，请检查配置')
    }
  } catch (error) {
    MessagePlugin.error('飞书连接测试失败')
  }
}

const resetFeishuToDefault = () => {
  const defaultFeishuConfig: FeishuConfig = {
    appToken: 'RR9cb8uqKaBUOGso9WTcBiXjnfh',
    tableId: 'tbl1arQX7A2jiLh4',
    internshipTableId: 'tbl2tPFOPenoIQdg',
    stateOwnedTableId: 'tbl1UfSuMtFZ8GR9',
    appId: 'cli_a80ce54e0c78100c',
    appSecret: '6l0TOvHiepUuHD5VyxmRwcXHLoRalwfQ'
  }
  feishuConfig.value = { ...defaultFeishuConfig }
  MessagePlugin.info('已恢复飞书默认配置')
}

// 数据库管理方法

const testMySQLConnection = async () => {
  isTestingConnection.value = true
  connectionStatus.value = null

  try {
    if (!validateMySQLConfig(mysqlConfig.value)) {
      throw new Error('MySQL配置不完整')
    }

    const client = createMySQLClient(mysqlConfig.value)
    const connected = await client.testConnection()

    if (connected) {
      connectionStatus.value = {
        success: true,
        message: 'MySQL连接测试成功！'
      }
    } else {
      throw new Error('连接失败')
    }
  } catch (error) {
    connectionStatus.value = {
      success: false,
      message: `连接测试失败: ${error}`
    }
  } finally {
    isTestingConnection.value = false
  }
}

// 用户管理方法
const loadUsers = () => {
  users.value = userService.getAllUsers()
}

const validateUserName = () => {
  userNameValidation.value = userService.validateUsername(newUserName.value)
}

const createUser = async () => {
  try {
    if (!userNameValidation.value.valid) {
      return
    }

    await userService.createUser(newUserName.value)
    loadUsers()
    newUserName.value = ''
    userNameValidation.value = { valid: false, message: '' }

    MessagePlugin.success('用户创建成功')
  } catch (error) {
    MessagePlugin.error(`创建用户失败: ${error}`)
  }
}

const setActiveUser = (user: LocalUser) => {
  userService.setCurrentUser(user)
  loadUsers()
  MessagePlugin.success(`已设置 ${user.username} 为当前用户`)
}

const deleteUser = async (userId: number) => {
  try {
    const user = users.value.find(u => u.id === userId)
    if (!user) return

    if (confirm(`确定要删除用户 "${user.username}" 吗？此操作不可恢复！`)) {
      await userService.deleteUser(userId)
      loadUsers()
      MessagePlugin.success('用户删除成功')
    }
  } catch (error) {
    MessagePlugin.error(`删除用户失败: ${error}`)
  }
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 应用配置并退出
const applyAndExit = () => {
  // 应用数据源选择
  dataSourceStore.setDataSource(selectedDataSource.value)
  MessagePlugin.success(`已切换到${selectedDataSource.value === 'mysql' ? 'MySQL数据库' : '飞书多维表格'}`)

  // 退出管理员模式
  setTimeout(() => {
    window.location.reload() // 刷新页面以应用配置
  }, 1000)
}

// 初始化
onMounted(() => {
  // 加载MySQL配置
  const savedMySQLConfig = loadMySQLConfigFromStorage()
  if (savedMySQLConfig) {
    mysqlConfig.value = savedMySQLConfig
  } else {
    mysqlConfig.value = { ...defaultMySQLConfig }
  }

  // 加载飞书配置
  const savedFeishuConfig = loadConfigFromStorage()
  if (savedFeishuConfig) {
    feishuConfig.value = savedFeishuConfig
  }

  // 加载用户数据
  loadUsers()
})
</script>

<style scoped>
.admin-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  margin: 20px 0;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-header h3 {
  margin: 0;
  color: white;
  font-size: 20px;
}

.admin-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.section-header h4 {
  margin: 0 0 8px 0;
  color: white;
  font-size: 16px;
}

.section-description {
  margin: 0 0 15px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.data-source-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.radio-option {
  padding: 10px 0;
}

.radio-title {
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.radio-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: white;
  font-size: 14px;
}

.config-input {
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.admin-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
  .admin-panel {
    padding: 15px;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .admin-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

/* 数据库管理样式 */
.database-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.connection-status {
  padding: 10px;
  border-radius: 6px;
  margin-top: 10px;
}

.connection-status .success {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 0;
}

.connection-status .error {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 0;
}

/* 用户管理样式 */
.user-management {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
}

.user-list h4,
.create-user-section h4 {
  margin: 0 0 15px 0;
  color: white;
  font-size: 16px;
}

.no-users {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.user-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.user-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.user-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.user-item.active {
  background: rgba(82, 196, 26, 0.2);
  border: 1px solid rgba(82, 196, 26, 0.4);
}

.username {
  font-weight: 600;
  color: white;
  font-size: 14px;
}

.user-info {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 8px;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.create-user-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

@media (max-width: 768px) {
  .user-management {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .database-actions {
    flex-direction: column;
  }

  .user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .user-actions {
    align-self: flex-end;
  }
}
</style>
