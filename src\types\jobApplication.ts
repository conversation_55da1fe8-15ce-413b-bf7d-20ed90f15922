// 求职申请相关类型定义

export interface JobApplication {
  id?: number
  userId?: number
  username?: string
  company: string
  applicationLink: string
  priority: number
  industry: string
  tags: string
  position: string
  location: string
  progress: string
  status: string
  progressDate: string
  applicationDate: string
  notes: string
  referralCode: string
  createdAt?: Date
  updatedAt?: Date
}

// MySQL数据库字段映射
export const MYSQL_FIELD_MAPPING = {
  // 本地字段名 -> MySQL字段名
  company: '公司',
  applicationLink: '投递链接',
  priority: '重视度',
  position: '职位',
  location: '地点',
  progress: '进展',
  status: '状态',
  progressDate: '进展时间',
  applicationDate: '投递时间',
  notes: '备注',
  referralCode: '内推码'
} as const

// MySQL字段名 -> 本地字段名 (反向映射)
export const MYSQL_FIELD_REVERSE_MAPPING = {
  '公司': 'company',
  '投递链接': 'applicationLink',
  '重视度': 'priority',
  '职位': 'position',
  '地点': 'location',
  '进展': 'progress',
  '状态': 'status',
  '进展时间': 'progressDate',
  '投递时间': 'applicationDate',
  '备注': 'notes',
  '内推码': 'referralCode'
} as const

// 数据转换工具函数
export const convertToMySQLFormat = (application: JobApplication): any => {
  const mysqlData: any = {
    username: application.username || '',
    [MYSQL_FIELD_MAPPING.company]: application.company || '',
    [MYSQL_FIELD_MAPPING.applicationLink]: application.applicationLink || '',
    [MYSQL_FIELD_MAPPING.priority]: application.priority || 2,
    [MYSQL_FIELD_MAPPING.position]: application.position || '',
    [MYSQL_FIELD_MAPPING.location]: application.location || '',
    [MYSQL_FIELD_MAPPING.progress]: application.progress || '',
    [MYSQL_FIELD_MAPPING.status]: application.status || '',
    [MYSQL_FIELD_MAPPING.progressDate]: application.progressDate || '',
    [MYSQL_FIELD_MAPPING.applicationDate]: application.applicationDate || '',
    [MYSQL_FIELD_MAPPING.notes]: application.notes || '',
    [MYSQL_FIELD_MAPPING.referralCode]: application.referralCode || ''
  }

  if (application.id) {
    mysqlData.id = application.id
  }

  return mysqlData
}

export const convertFromMySQLFormat = (mysqlData: any): JobApplication => {
  return {
    id: mysqlData.id,
    username: mysqlData.username || '',
    company: mysqlData[MYSQL_FIELD_MAPPING.company] || '',
    applicationLink: mysqlData[MYSQL_FIELD_MAPPING.applicationLink] || '',
    priority: mysqlData[MYSQL_FIELD_MAPPING.priority] || 2,
    industry: '', // MySQL中没有对应字段，保持为空
    tags: '', // MySQL中没有对应字段，保持为空
    position: mysqlData[MYSQL_FIELD_MAPPING.position] || '',
    location: mysqlData[MYSQL_FIELD_MAPPING.location] || '',
    progress: mysqlData[MYSQL_FIELD_MAPPING.progress] || '',
    status: mysqlData[MYSQL_FIELD_MAPPING.status] || '',
    progressDate: mysqlData[MYSQL_FIELD_MAPPING.progressDate] || '',
    applicationDate: mysqlData[MYSQL_FIELD_MAPPING.applicationDate] || '',
    notes: mysqlData[MYSQL_FIELD_MAPPING.notes] || '',
    referralCode: mysqlData[MYSQL_FIELD_MAPPING.referralCode] || ''
  }
}

export interface JobApplicationFilter {
  company?: string
  industry?: string
  position?: string
  location?: string
  progress?: string
  status?: string
  priority?: number
}

export interface JobApplicationSort {
  field: keyof JobApplication
  order: 'asc' | 'desc'
}
