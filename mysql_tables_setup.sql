-- =====================================================
-- 求职申请跟踪系统 MySQL 数据库表结构（合并表方案）
-- 基于 JobApplicationTable.vue 的字段需求设计
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 求职申请数据表（包含用户信息）- 合并表方案
-- 基于 JobApplicationTable.vue 中的字段设计
-- =====================================================
CREATE TABLE IF NOT EXISTS job_applications_with_user (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID，主键',

  -- 用户信息字段
  username VARCHAR(255) NOT NULL COMMENT '用户名/求职者姓名',

  -- 求职申请核心字段（对应 JobApplicationTable.vue 中的字段）
  company VARCHAR(255) NOT NULL DEFAULT '' COMMENT '公司名称',
  applicationLink TEXT COMMENT '投递链接',
  priority INT DEFAULT 2 COMMENT '重视度/优先级（1-高，2-中，3-低）',
  industry VARCHAR(255) DEFAULT '' COMMENT '行业',
  tags TEXT COMMENT '技能标签',
  position VARCHAR(255) DEFAULT '' COMMENT '职位名称',
  location VARCHAR(255) DEFAULT '' COMMENT '工作地点',
  progress VARCHAR(100) DEFAULT '' COMMENT '进展状态',
  status VARCHAR(100) DEFAULT '' COMMENT '申请状态',
  progressDate DATE NULL COMMENT '进展时间',
  applicationDate DATE NULL COMMENT '投递时间',
  notes TEXT COMMENT '备注信息',
  referralCode VARCHAR(255) DEFAULT '' COMMENT '内推码',

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- 索引优化
  INDEX idx_username (username),
  INDEX idx_company (company),
  INDEX idx_position (position),
  INDEX idx_progress (progress),
  INDEX idx_status (status),
  INDEX idx_priority (priority),
  INDEX idx_application_date (applicationDate),
  INDEX idx_progress_date (progressDate),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='求职申请数据表（包含用户信息）';

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 3. 示例数据插入
-- =====================================================

-- 插入示例求职申请数据
INSERT IGNORE INTO job_applications_with_user (
  username, company, applicationLink, priority, industry, tags,
  position, location, progress, status, progressDate, applicationDate,
  notes, referralCode
) VALUES
('张三', '腾讯', 'https://careers.tencent.com/apply/123', 1, '互联网', 'Vue,React,Node.js',
 '前端工程师', '深圳', '面试中', '进行中', '2024-01-15', '2024-01-10',
 '技术面试表现良好', 'TC2024001'),

('张三', '阿里巴巴', 'https://job.alibaba.com/apply/456', 1, '互联网', 'Vue,TypeScript',
 '高级前端工程师', '杭州', '已投递', '待回复', NULL, '2024-01-12',
 '通过内推投递', 'ALI2024002'),

('李四', '字节跳动', 'https://jobs.bytedance.com/apply/789', 2, '互联网', 'React,Node.js',
 '全栈工程师', '北京', '笔试', '进行中', '2024-01-14', '2024-01-08',
 '在线笔试已完成', 'BD2024003'),

('王五', '美团', 'https://zhaopin.meituan.com/apply/101', 2, '互联网', 'Java,Spring',
 'Java开发工程师', '北京', '已投递', '待回复', NULL, '2024-01-16',
 '校招投递', 'MT2024004');

-- =====================================================
-- 4. 查询示例
-- =====================================================

-- 查询所有用户及其申请数量
SELECT
  username,
  COUNT(CASE WHEN company != '' THEN 1 END) as application_count,
  MIN(created_at) as first_application,
  MAX(updated_at) as last_updated
FROM job_applications_with_user
GROUP BY username
ORDER BY application_count DESC;

-- 查询特定用户的求职申请
SELECT
  id,
  company,
  position,
  location,
  progress,
  status,
  applicationDate,
  progressDate,
  priority,
  notes
FROM job_applications_with_user
WHERE username = '张三' AND company != ''
ORDER BY applicationDate DESC;

-- 查询各公司的申请统计
SELECT
  company,
  COUNT(*) as total_applications,
  COUNT(CASE WHEN progress IN ('面试中', '终面') THEN 1 END) as interview_count,
  COUNT(CASE WHEN status = '已录用' THEN 1 END) as offer_count,
  COUNT(DISTINCT username) as applicant_count
FROM job_applications_with_user
WHERE company != ''
GROUP BY company
ORDER BY total_applications DESC;

-- 查询各状态的申请分布
SELECT
  progress,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM job_applications_with_user WHERE company != ''), 2) as percentage
FROM job_applications_with_user
WHERE company != ''
GROUP BY progress
ORDER BY count DESC;

-- =====================================================
-- 5. 维护脚本
-- =====================================================

-- 清理测试数据（谨慎使用）
-- DELETE FROM job_applications_with_user WHERE notes LIKE '%测试%';
-- DELETE FROM job_applications_with_user WHERE username LIKE '%test%';

-- 重置自增ID（谨慎使用）
-- ALTER TABLE job_applications_with_user AUTO_INCREMENT = 1;

-- 查看表结构
-- DESCRIBE job_applications_with_user;

-- 查看索引信息
-- SHOW INDEX FROM job_applications_with_user;

-- =====================================================
-- 6. 性能优化建议
-- =====================================================

-- 如果数据量很大，可以考虑添加分区
-- ALTER TABLE job_applications_with_user
-- PARTITION BY RANGE (YEAR(created_at)) (
--   PARTITION p2023 VALUES LESS THAN (2024),
--   PARTITION p2024 VALUES LESS THAN (2025),
--   PARTITION p2025 VALUES LESS THAN (2026),
--   PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 定期清理旧数据的存储过程示例
-- DELIMITER //
-- CREATE PROCEDURE CleanOldApplications()
-- BEGIN
--   DELETE FROM job_applications_with_user
--   WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR)
--   AND status IN ('已拒绝', '已放弃')
--   AND company != '';
-- END //
-- DELIMITER ;

-- =====================================================
-- 说明：
-- 1. 此脚本基于 JobApplicationTable.vue 中的字段需求设计
-- 2. 包含完整的索引优化，支持高效查询
-- 3. 使用 utf8mb4 字符集，支持 emoji 和特殊字符
-- 4. 包含外键约束，保证数据完整性
-- 5. 提供了示例数据和常用查询语句
-- =====================================================
