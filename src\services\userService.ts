// 用户管理服务
import { databaseService } from './database'
import type { MySQLUser } from './mysqlApi'

// 本地用户接口
export interface LocalUser {
  id?: number
  username: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// 用户服务类
export class UserService {
  private static instance: UserService
  private currentUser: LocalUser | null = null
  private users: LocalUser[] = []

  private constructor() {
    this.loadFromStorage()
  }

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }

  /**
   * 从本地存储加载用户数据
   */
  private loadFromStorage(): void {
    try {
      const usersData = localStorage.getItem('app_users')
      if (usersData) {
        this.users = JSON.parse(usersData).map((user: any) => ({
          ...user,
          createdAt: new Date(user.createdAt),
          updatedAt: new Date(user.updatedAt)
        }))
      }

      const currentUserData = localStorage.getItem('current_user')
      if (currentUserData) {
        const userData = JSON.parse(currentUserData)
        this.currentUser = {
          ...userData,
          createdAt: new Date(userData.createdAt),
          updatedAt: new Date(userData.updatedAt)
        }
      }

      console.log('用户数据加载完成:', this.users.length, '个用户')
    } catch (error) {
      console.error('加载用户数据失败:', error)
      this.users = []
      this.currentUser = null
    }
  }

  /**
   * 保存用户数据到本地存储
   */
  private saveToStorage(): void {
    try {
      localStorage.setItem('app_users', JSON.stringify(this.users))
      if (this.currentUser) {
        localStorage.setItem('current_user', JSON.stringify(this.currentUser))
      } else {
        localStorage.removeItem('current_user')
      }
    } catch (error) {
      console.error('保存用户数据失败:', error)
    }
  }

  /**
   * 获取所有用户
   */
  getAllUsers(): LocalUser[] {
    return [...this.users]
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): LocalUser | null {
    return this.currentUser
  }

  /**
   * 创建新用户
   */
  async createUser(username: string): Promise<LocalUser> {
    // 验证用户名
    if (!username || username.trim() === '') {
      throw new Error('用户名不能为空')
    }

    const trimmedUsername = username.trim()

    // 检查用户名是否已存在
    if (this.users.some(user => user.username === trimmedUsername)) {
      throw new Error('用户名已存在')
    }

    // 创建新用户
    const newUser: LocalUser = {
      id: Date.now(),
      username: trimmedUsername,
      isActive: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.users.push(newUser)
    this.saveToStorage()

    // 保存到数据库
    try {
      await databaseService.setSetting(`user_${newUser.id}`, JSON.stringify(newUser))
    } catch (error) {
      console.error('保存用户到数据库失败:', error)
    }

    console.log('创建用户成功:', trimmedUsername)
    return newUser
  }

  /**
   * 设置当前用户
   */
  setCurrentUser(user: LocalUser | null): void {
    // 重置所有用户的活跃状态
    this.users.forEach(u => u.isActive = false)

    if (user) {
      // 设置新的当前用户
      const userIndex = this.users.findIndex(u => u.id === user.id)
      if (userIndex !== -1) {
        this.users[userIndex].isActive = true
        this.users[userIndex].updatedAt = new Date()
        this.currentUser = this.users[userIndex]
      }
    } else {
      this.currentUser = null
    }

    this.saveToStorage()
    console.log('当前用户已设置:', user?.username || '无')
  }

  /**
   * 删除用户
   */
  async deleteUser(userId: number): Promise<void> {
    const userIndex = this.users.findIndex(user => user.id === userId)
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    const user = this.users[userIndex]

    // 如果删除的是当前用户，清空当前用户
    if (this.currentUser && this.currentUser.id === userId) {
      this.currentUser = null
    }

    // 从数组中移除
    this.users.splice(userIndex, 1)
    this.saveToStorage()

    // 从数据库中删除
    try {
      await databaseService.setSetting(`user_${userId}`, '')
    } catch (error) {
      console.error('从数据库删除用户失败:', error)
    }

    console.log('删除用户成功:', user.username)
  }

  /**
   * 更新用户信息
   */
  async updateUser(userId: number, updates: Partial<LocalUser>): Promise<LocalUser> {
    const userIndex = this.users.findIndex(user => user.id === userId)
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    // 如果更新用户名，检查是否重复
    if (updates.username && updates.username !== this.users[userIndex].username) {
      const trimmedUsername = updates.username.trim()
      if (this.users.some(user => user.username === trimmedUsername && user.id !== userId)) {
        throw new Error('用户名已存在')
      }
      updates.username = trimmedUsername
    }

    // 更新用户信息
    this.users[userIndex] = {
      ...this.users[userIndex],
      ...updates,
      updatedAt: new Date()
    }

    // 如果更新的是当前用户，同步更新
    if (this.currentUser && this.currentUser.id === userId) {
      this.currentUser = this.users[userIndex]
    }

    this.saveToStorage()

    // 保存到数据库
    try {
      await databaseService.setSetting(`user_${userId}`, JSON.stringify(this.users[userIndex]))
    } catch (error) {
      console.error('更新用户到数据库失败:', error)
    }

    console.log('更新用户成功:', this.users[userIndex].username)
    return this.users[userIndex]
  }

  /**
   * 根据用户名查找用户
   */
  findUserByUsername(username: string): LocalUser | null {
    return this.users.find(user => user.username === username) || null
  }

  /**
   * 验证用户名格式
   */
  validateUsername(username: string): { valid: boolean; message: string } {
    if (!username || username.trim() === '') {
      return { valid: false, message: '用户名不能为空' }
    }

    const trimmedUsername = username.trim()

    if (trimmedUsername.length < 2) {
      return { valid: false, message: '用户名至少需要2个字符' }
    }

    if (trimmedUsername.length > 50) {
      return { valid: false, message: '用户名不能超过50个字符' }
    }

    // 检查特殊字符
    const validPattern = /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/
    if (!validPattern.test(trimmedUsername)) {
      return { valid: false, message: '用户名只能包含字母、数字、中文、下划线和连字符' }
    }

    return { valid: true, message: '' }
  }

  /**
   * 初始化默认用户
   */
  async initializeDefaultUser(): Promise<void> {
    if (this.users.length === 0) {
      try {
        const defaultUser = await this.createUser('默认用户')
        this.setCurrentUser(defaultUser)
        console.log('已创建默认用户')
      } catch (error) {
        console.error('创建默认用户失败:', error)
      }
    } else if (!this.currentUser) {
      // 如果有用户但没有当前用户，设置第一个用户为当前用户
      this.setCurrentUser(this.users[0])
    }
  }

  /**
   * 获取用户统计信息
   */
  getUserStats(): { totalUsers: number; activeUser: string | null; lastCreated: Date | null } {
    const lastCreated = this.users.length > 0 
      ? new Date(Math.max(...this.users.map(u => u.createdAt.getTime())))
      : null

    return {
      totalUsers: this.users.length,
      activeUser: this.currentUser?.username || null,
      lastCreated
    }
  }
}

// 导出单例实例
export const userService = UserService.getInstance()
