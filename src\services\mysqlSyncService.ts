// MySQL同步服务
import { createMySQLClient, loadMySQLConfigFromStorage, type MySQLConfig, type MySQLUser } from './mysqlApi'
import { databaseService } from './database'
import { userService, type LocalUser } from './userService'
import type { JobApplication } from '../types/jobApplication'
import { convertToMySQLFormat, convertFromMySQLFormat } from '../types/jobApplication'

export interface SyncResult {
  success: boolean
  message: string
  uploadedCount?: number
  downloadedCount?: number
  errors?: string[]
  conflictCount?: number
  skippedCount?: number
}

export interface SyncOptions {
  direction: 'upload' | 'download' | 'bidirectional'
  userId?: number
  username?: string
  overwrite?: boolean
  autoCreateUser?: boolean
  conflictResolution?: 'local' | 'remote' | 'newer' | 'ask'
}

export class MySQLSyncService {
  private static instance: MySQLSyncService
  private mysqlClient: any = null
  private isConnected = false

  private constructor() {}

  static getInstance(): MySQLSyncService {
    if (!MySQLSyncService.instance) {
      MySQLSyncService.instance = new MySQLSyncService()
    }
    return MySQLSyncService.instance
  }

  /**
   * 初始化MySQL连接
   */
  async initializeConnection(): Promise<boolean> {
    try {
      const config = loadMySQLConfigFromStorage()
      if (!config) {
        console.error('MySQL配置未找到')
        return false
      }

      this.mysqlClient = createMySQLClient(config)
      this.isConnected = await this.mysqlClient.testConnection()

      if (this.isConnected) {
        console.log('MySQL同步服务连接初始化成功')
        // 确保表存在
        await this.ensureTablesExist()
      } else {
        console.error('MySQL同步服务连接测试失败')
      }

      return this.isConnected
    } catch (error) {
      console.error('初始化MySQL同步服务连接失败:', error)
      this.isConnected = false
      return false
    }
  }

  /**
   * 重新连接MySQL
   */
  async reconnect(): Promise<boolean> {
    if (this.mysqlClient) {
      await this.mysqlClient.close()
      this.mysqlClient = null
    }
    this.isConnected = false
    return await this.initializeConnection()
  }

  /**
   * 检查MySQL表是否存在
   */
  private async ensureTablesExist(): Promise<void> {
    try {
      if (!this.mysqlClient) {
        throw new Error('MySQL客户端未初始化')
      }

      // 检查userdata表是否存在
      const connection = await this.mysqlClient.getConnection()
      const [rows] = await connection.query("SHOW TABLES LIKE 'userdata'")

      if (rows.length === 0) {
        throw new Error('userdata表不存在，请先在MySQL中创建该表')
      }

      console.log('MySQL表检查完成')
    } catch (error) {
      console.error('检查MySQL表失败:', error)
      throw error
    }
  }

  /**
   * 检查连接状态
   */
  async checkConnection(): Promise<boolean> {
    if (!this.mysqlClient) {
      return await this.initializeConnection()
    }

    try {
      this.isConnected = await this.mysqlClient.testConnection()
      return this.isConnected
    } catch (error) {
      console.error('检查MySQL连接失败:', error)
      this.isConnected = false
      return false
    }
  }

  /**
   * 同步用户数据到MySQL
   */
  async syncUsersToMySQL(): Promise<SyncResult> {
    try {
      if (!await this.checkConnection()) {
        return { success: false, message: 'MySQL连接失败' }
      }

      const localUsers = userService.getAllUsers()
      const errors: string[] = []
      let syncedCount = 0

      for (const localUser of localUsers) {
        try {
          // 检查用户是否已存在
          const existingUsers = await this.mysqlClient.getAllUsers()
          const existingUser = existingUsers.find((u: MySQLUser) => u.username === localUser.username)

          if (!existingUser) {
            await this.mysqlClient.createUser(localUser.username)
            syncedCount++
            console.log(`用户 ${localUser.username} 已同步到MySQL`)
          }
        } catch (error) {
          const errorMsg = `同步用户 ${localUser.username} 失败: ${error}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      return {
        success: errors.length === 0,
        message: `用户同步完成，成功同步 ${syncedCount} 个用户`,
        uploadedCount: syncedCount,
        errors: errors.length > 0 ? errors : undefined
      }
    } catch (error) {
      console.error('同步用户到MySQL失败:', error)
      return { success: false, message: `同步失败: ${error}` }
    }
  }

  /**
   * 上传求职申请数据到MySQL
   */
  async uploadJobApplications(options: SyncOptions): Promise<SyncResult> {
    try {
      if (!await this.checkConnection()) {
        return { success: false, message: 'MySQL连接失败' }
      }

      const username = options.username
      if (!username) {
        return { success: false, message: '请提供用户名' }
      }

      // 确保本地用户存在
      let localUser = userService.findUserByUsername(username)
      if (!localUser && options.autoCreateUser) {
        localUser = await userService.createUser(username)
        userService.setCurrentUser(localUser)
      } else if (!localUser) {
        return { success: false, message: '本地用户不存在' }
      }

      // 获取本地数据
      console.log(`获取用户 ${username} 的本地数据...`)
      const localApplications = await databaseService.getJobApplicationsByUsername(username)
      console.log(`找到 ${localApplications.length} 条本地记录`)

      if (localApplications.length === 0) {
        console.log('没有需要上传的数据')
        return { success: true, message: '没有需要上传的数据', uploadedCount: 0 }
      }

      // 直接上传数据，不需要预先创建用户

      // 过滤掉空的申请记录
      const validApplications = localApplications.filter(app =>
        app.company && app.company.trim() !== ''
      )

      if (validApplications.length === 0) {
        return {
          success: true,
          message: '没有有效的求职申请数据需要上传',
          uploadedCount: 0
        }
      }

      // 批量上传到MySQL
      console.log(`开始批量上传 ${validApplications.length} 条记录到MySQL...`)
      await this.mysqlClient.batchSaveJobApplications(validApplications)
      console.log('批量上传完成')

      return {
        success: true,
        message: `成功上传 ${validApplications.length} 条求职申请数据到MySQL`,
        uploadedCount: validApplications.length
      }
    } catch (error) {
      console.error('上传求职申请数据失败:', error)
      return { success: false, message: `上传失败: ${error}` }
    }
  }

  /**
   * 从MySQL下载求职申请数据
   */
  async downloadJobApplications(options: SyncOptions): Promise<SyncResult> {
    try {
      if (!await this.checkConnection()) {
        return { success: false, message: 'MySQL连接失败' }
      }

      const username = options.username
      if (!username) {
        return { success: false, message: '请提供用户名' }
      }

      // 确保本地用户存在
      let localUser = userService.findUserByUsername(username)
      if (!localUser && options.autoCreateUser) {
        localUser = await userService.createUser(username)
        userService.setCurrentUser(localUser)
      } else if (!localUser) {
        return { success: false, message: '本地用户不存在' }
      }

      // 获取MySQL中的数据
      console.log(`从MySQL获取用户 ${username} 的数据...`)
      const mysqlApplications = await this.mysqlClient.getUserJobApplications(username)
      console.log(`从MySQL获取到 ${mysqlApplications.length} 条记录`)

      if (mysqlApplications.length === 0) {
        return { success: true, message: 'MySQL中没有该用户的求职申请数据', downloadedCount: 0 }
      }

      // 数据已经通过convertFromMySQLFormat转换为标准格式
      const localApplications: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>[] = mysqlApplications.map(app => ({
        userId: localUser!.id,
        username: username,
        company: app.company,
        applicationLink: app.applicationLink,
        priority: app.priority,
        industry: app.industry || '', // 保持原有值或空
        tags: app.tags || '', // 保持原有值或空
        position: app.position,
        location: app.location,
        progress: app.progress,
        status: app.status,
        progressDate: app.progressDate,
        applicationDate: app.applicationDate,
        notes: app.notes,
        referralCode: app.referralCode
      }))

      // 替换本地数据
      await databaseService.replaceUserJobApplications(
        localUser!.id!,
        username,
        localApplications
      )

      return {
        success: true,
        message: `成功从MySQL下载 ${mysqlApplications.length} 条求职申请数据`,
        downloadedCount: mysqlApplications.length
      }
    } catch (error) {
      console.error('下载求职申请数据失败:', error)
      return { success: false, message: `下载失败: ${error}` }
    }
  }

  /**
   * 双向同步
   */
  async bidirectionalSync(options: SyncOptions): Promise<SyncResult> {
    try {
      // 先上传本地数据
      const uploadResult = await this.uploadJobApplications(options)
      if (!uploadResult.success) {
        return uploadResult
      }

      // 再下载MySQL数据
      const downloadResult = await this.downloadJobApplications(options)
      if (!downloadResult.success) {
        return downloadResult
      }

      return {
        success: true,
        message: `双向同步完成，上传 ${uploadResult.uploadedCount || 0} 条，下载 ${downloadResult.downloadedCount || 0} 条`,
        uploadedCount: uploadResult.uploadedCount,
        downloadedCount: downloadResult.downloadedCount
      }
    } catch (error) {
      console.error('双向同步失败:', error)
      return { success: false, message: `双向同步失败: ${error}` }
    }
  }



  /**
   * 获取连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  /**
   * 关闭连接
   */
  async closeConnection(): Promise<void> {
    if (this.mysqlClient) {
      await this.mysqlClient.close()
      this.mysqlClient = null
      this.isConnected = false
    }
  }
}

// 导出单例实例
export const mysqlSyncService = MySQLSyncService.getInstance()
