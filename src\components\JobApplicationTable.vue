<template>
  <div class="job-application-table">
    <div class="table-header">
      <div class="header-actions">
        <!-- 用户名输入/选择器 -->
        <div class="user-selector">
          <label>用户名:</label>
          <!-- 管理员模式：下拉选择框 -->
          <t-select
            v-if="isAdminMode"
            v-model="currentUsername"
            :options="userOptions"
            placeholder="选择用户"
            @change="onAdminUserChange"
            style="width: 180px;"
            size="medium"
            filterable
            clearable
          />
          <!-- 普通用户模式：文本输入框 -->
          <t-input
            v-else
            v-model="currentUsername"
            placeholder="输入用户名"
            @blur="onUsernameChange"
            @keyup.enter="() => onUsernameChange()"
            style="width: 180px;"
          />

          <!-- 管理员模式下的创建用户按钮 -->
          <t-button
            v-if="isAdminMode"
            theme="default"
            variant="outline"
            size="small"
            @click="showCreateUserDialog = true"
            title="创建新用户"
          >
            <template #icon><user-add-icon /></template>
          </t-button>
        </div>



        <!-- 原有按钮 -->
        <div class="table-actions">
          <t-button
            theme="danger"
            variant="outline"
            :disabled="selectedRowKeys.length === 0"
            @click="batchDelete"
          >
            <template #icon><delete-icon /></template>
            批量删除 ({{ selectedRowKeys.length }})
          </t-button>
          <t-button theme="primary" @click="addNewRow">
            <template #icon><add-icon /></template>
            添加新记录
          </t-button>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <t-table
        :data="filteredAndSortedData"
        :columns="columns"
        row-key="id"
        :loading="store.loading"
        :selected-row-keys="selectedRowKeys"
        bordered
        stripe
        hover
        :resizable="true"
        :drag-sort="'col'"
        @select-change="onSelectChange"
        @filter-change="onFilterChange"
        @drag-sort="onColumnDragSort"
      >
        <!-- 自定义空数据状态 -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <svg width="200" height="200" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <div class="empty-title">暂无求职申请记录</div>
            <div class="empty-description">
              点击"添加新记录"按钮开始记录您的求职申请，或者从校招汇总表中添加感兴趣的职位
            </div>
            <div class="empty-actions">
              <t-button theme="primary" @click="addNewRow">
                <template #icon><add-icon /></template>
                添加第一条记录
              </t-button>
            </div>
          </div>
        </template>
        <!-- 重视度列标题 -->
        <template #priority-title>
          <div class="custom-header">
            <span>重视度</span>
            <t-button
              variant="text"
              size="small"
              @click="toggleSort('priority')"
              :title="getSortTitle('priority')"
              class="sort-button"
            >
              <component :is="getSortIcon('priority')" />
            </t-button>
          </div>
        </template>

        <!-- 进展时间列标题 -->
        <template #progressDate-title>
          <div class="custom-header">
            <span>进展时间</span>
            <div class="header-buttons">
              <t-button
                variant="text"
                size="small"
                @click="toggleSort('progressDate')"
                :title="getSortTitle('progressDate')"
                class="sort-button"
              >
                <component :is="getSortIcon('progressDate')" />
              </t-button>
              <t-button
                variant="text"
                size="small"
                @click="toggleDateFilter('progressDate')"
                :title="getFilterTitle('progressDate')"
                class="filter-button"
                :class="{ 'filter-active': isDateFilterActive('progressDate') }"
              >
                <filter-icon />
              </t-button>
            </div>
          </div>
        </template>

        <!-- 投递时间列标题 -->
        <template #applicationDate-title>
          <div class="custom-header">
            <span>投递时间</span>
            <div class="header-buttons">
              <t-button
                variant="text"
                size="small"
                @click="toggleSort('applicationDate')"
                :title="getSortTitle('applicationDate')"
                class="sort-button"
              >
                <component :is="getSortIcon('applicationDate')" />
              </t-button>
              <t-button
                variant="text"
                size="small"
                @click="toggleDateFilter('applicationDate')"
                :title="getFilterTitle('applicationDate')"
                class="filter-button"
                :class="{ 'filter-active': isDateFilterActive('applicationDate') }"
              >
                <filter-icon />
              </t-button>
            </div>
          </div>
        </template>

        <!-- 公司名称 -->
        <template #company="{ row, rowIndex }">
        <t-input
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'company'"
          v-model="editingValue"
          @blur="saveEdit(rowIndex, 'company')"
          @keyup="(e) => handleKeyup(e, rowIndex, 'company')"
          autofocus
        />
        <span v-else @click="startEdit(rowIndex, 'company', row.company)" class="editable-cell">
          {{ row.company || '' }}
        </span>
      </template>

      <!-- 投递链接 -->
      <template #applicationLink="{ row, rowIndex }">
        <t-input
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'applicationLink'"
          v-model="editingValue"
          @blur="saveEdit(rowIndex, 'applicationLink')"
          @keyup="(e) => handleKeyup(e, rowIndex, 'applicationLink')"
          autofocus
        />
        <span
          v-else
          @click="startEdit(rowIndex, 'applicationLink', row.applicationLink)"
          @contextmenu.prevent="openLink(row.applicationLink)"
          class="editable-cell link-cell"
          :class="{ 'has-link': isValidUrl(row.applicationLink) }"
          :title="isValidUrl(row.applicationLink) ? '左键编辑，右键打开链接' : '点击编辑'"
        >
          {{ row.applicationLink || '' }}
        </span>
      </template>

      <!-- 重视度 -->
      <template #priority="{ row, rowIndex }">
        <t-select
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'priority'"
          v-model="editingValue"
          @change="saveEdit(rowIndex, 'priority')"
          @blur="cancelEdit"
          :options="priorityOptions"
          size="small"
          autofocus
          :popup-props="{ overlayClassName: 'custom-select-popup' }"
        />
        <t-tag
          v-else
          @click="startEdit(rowIndex, 'priority', row.priority)"
          :theme="getPriorityTheme(row.priority)"
          class="editable-cell priority-tag"
        >
          {{ getPriorityText(row.priority) }}
        </t-tag>
      </template>



      <!-- 职位 -->
      <template #position="{ row, rowIndex }">
        <t-input
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'position'"
          v-model="editingValue"
          @blur="saveEdit(rowIndex, 'position')"
          @keyup="(e) => handleKeyup(e, rowIndex, 'position')"
          autofocus
        />
        <span v-else @click="startEdit(rowIndex, 'position', row.position)" class="editable-cell">
          {{ row.position || '' }}
        </span>
      </template>

      <!-- 地点 -->
      <template #location="{ row, rowIndex }">
        <t-input
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'location'"
          v-model="editingValue"
          @blur="saveEdit(rowIndex, 'location')"
          @keyup="(e) => handleKeyup(e, rowIndex, 'location')"
          autofocus
        />
        <span v-else @click="startEdit(rowIndex, 'location', row.location)" class="editable-cell">
          {{ row.location || '' }}
        </span>
      </template>

      <!-- 进展 -->
      <template #progress="{ row, rowIndex }">
        <t-select
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'progress'"
          v-model="editingValue"
          @change="saveEdit(rowIndex, 'progress')"
          @blur="cancelEdit"
          :options="progressOptions"
          size="small"
          autofocus
          :popup-props="{ overlayClassName: 'custom-select-popup' }"
        />
        <span v-else @click="startEdit(rowIndex, 'progress', row.progress)" class="editable-cell">
          {{ row.progress || '' }}
        </span>
      </template>

      <!-- 状态进度 -->
      <template #status="{ row, rowIndex }">
        <t-select
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'status'"
          v-model="editingValue"
          @change="saveEdit(rowIndex, 'status')"
          @blur="cancelEdit"
          :options="statusOptions"
          size="small"
          autofocus
          :popup-props="{ overlayClassName: 'custom-select-popup' }"
        />
        <span v-else @click="startEdit(rowIndex, 'status', row.status)" class="editable-cell">
          {{ row.status || '' }}
        </span>
      </template>

      <!-- 进展时间 -->
      <template #progressDate="{ row, rowIndex }">
        <t-date-picker
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'progressDate'"
          v-model="editingValue"
          @pick="(value) => handleDateChangeWithParams(rowIndex, 'progressDate', value)"
          format="YYYY-MM-DD"
          autofocus
        />
        <span v-else @click="startEdit(rowIndex, 'progressDate', row.progressDate)" class="editable-cell">
          {{ row.progressDate || '' }}
        </span>
      </template>

      <!-- 投递时间 -->
      <template #applicationDate="{ row, rowIndex }">
        <t-date-picker
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'applicationDate'"
          v-model="editingValue"
          @pick="(value) => handleDateChangeWithParams(rowIndex, 'applicationDate', value)"
          format="YYYY-MM-DD"
          autofocus
        />
        <span v-else @click="startEdit(rowIndex, 'applicationDate', row.applicationDate)" class="editable-cell">
          {{ row.applicationDate || '' }}
        </span>
      </template>

      <!-- 备注 -->
      <template #notes="{ row, rowIndex }">
        <t-input
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'notes'"
          v-model="editingValue"
          @blur="saveEdit(rowIndex, 'notes')"
          @keyup="(e) => handleKeyup(e, rowIndex, 'notes')"
          autofocus
        />
        <span v-else @click="startEdit(rowIndex, 'notes', row.notes)" class="editable-cell">
          {{ row.notes || '' }}
        </span>
      </template>

      <!-- 内推码 -->
      <template #referralCode="{ row, rowIndex }">
        <t-input
          v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'referralCode'"
          v-model="editingValue"
          @blur="saveEdit(rowIndex, 'referralCode')"
          @keyup="(e) => handleKeyup(e, rowIndex, 'referralCode')"
          autofocus
        />
        <span v-else @click="startEdit(rowIndex, 'referralCode', row.referralCode)" class="editable-cell">
          {{ row.referralCode || '' }}
        </span>
      </template>


      </t-table>
    </div>

    <!-- 日期筛选弹窗 -->
    <t-dialog
      v-model:visible="dateFilterVisible.progressDate"
      title="进展时间筛选"
      width="600px"
      @confirm="handleDateFilterConfirm('progressDate')"
      @cancel="handleDateFilterCancel('progressDate')"
    >
      <div class="date-filter-content">
        <div class="date-range-section">
          <label>选择日期范围：</label>
          <t-date-range-picker
            v-model="tempDateRangeFilter.progressDate"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
            mode="date"
            :enable-time-picker="false"
            clearable
            separator="至"
          />
        </div>
        <div class="date-filter-tips">
          <p>• 只选择开始日期：筛选该日期之后的记录</p>
          <p>• 只选择结束日期：筛选该日期之前的记录</p>
          <p>• 选择日期范围：筛选指定范围内的记录</p>
        </div>
      </div>
      <template #footer>
        <t-button variant="outline" @click="clearDateFilter('progressDate')">清除</t-button>
        <t-button @click="handleDateFilterConfirm('progressDate')">确定</t-button>
      </template>
    </t-dialog>

    <t-dialog
      v-model:visible="dateFilterVisible.applicationDate"
      title="投递时间筛选"
      width="600px"
      @confirm="handleDateFilterConfirm('applicationDate')"
      @cancel="handleDateFilterCancel('applicationDate')"
    >
      <div class="date-filter-content">
        <div class="date-range-section">
          <label>选择日期范围：</label>
          <t-date-range-picker
            v-model="tempDateRangeFilter.applicationDate"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
            mode="date"
            :enable-time-picker="false"
            clearable
            separator="至"
          />
        </div>
        <div class="date-filter-tips">
          <p>• 只选择开始日期：筛选该日期之后的记录</p>
          <p>• 只选择结束日期：筛选该日期之前的记录</p>
          <p>• 选择日期范围：筛选指定范围内的记录</p>
        </div>
      </div>
      <template #footer>
        <t-button variant="outline" @click="clearDateFilter('applicationDate')">清除</t-button>
        <t-button @click="handleDateFilterConfirm('applicationDate')">确定</t-button>
      </template>
    </t-dialog>

    <!-- 创建用户对话框 -->
    <t-dialog
      v-model:visible="showCreateUserDialog"
      title="创建新用户"
      width="400px"
      @confirm="createNewUser"
      @cancel="cancelCreateUser"
    >
      <div class="create-user-form">
        <div class="form-group">
          <label>用户名:</label>
          <t-input
            v-model="newUsername"
            placeholder="请输入用户名"
            :status="usernameValidation.valid ? 'default' : 'error'"
            :tips="usernameValidation.message"
            @input="validateNewUsername"
          />
        </div>
      </div>
      <template #footer>
        <t-button variant="outline" @click="cancelCreateUser">取消</t-button>
        <t-button
          theme="primary"
          @click="createNewUser"
          :disabled="!usernameValidation.valid || !newUsername.trim()"
        >
          创建
        </t-button>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import {
  Table as TTable,
  Button as TButton,
  Input as TInput,
  Select as TSelect,
  DatePicker as TDatePicker,
  DateRangePicker as TDateRangePicker,
  Tag as TTag,
  Dialog as TDialog,
  MessagePlugin
} from 'tdesign-vue-next'
import {
  AddIcon,
  DeleteIcon,
  SwapIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  FilterIcon,
  UserAddIcon
} from 'tdesign-icons-vue-next'
import { useJobApplicationStore } from '../stores/jobApplicationStore'
import { userService } from '../services/userService'

import { useAdminState, adminService } from '../services/adminService'

// 使用Pinia store
const store = useJobApplicationStore()

// 管理员状态
const { isAdminMode } = useAdminState()

// 用户管理状态
const currentUsername = ref('')
const userOptions = ref<{ label: string; value: string }[]>([])
const showCreateUserDialog = ref(false)
const newUsername = ref('')
const usernameValidation = ref({ valid: false, message: '' })



// 编辑状态
const editingCell = reactive({
  rowIndex: -1,
  column: ''
})
const editingValue = ref('')

// 多选状态
const selectedRowKeys = ref<(string | number)[]>([])

// 筛选和排序状态
const filterValues = ref<Record<string, any>>({})
const currentSort = ref<{ sortBy?: string; descending?: boolean }>({})

// 排序状态：0=无排序, 1=升序, 2=降序
const sortStates = ref<Record<string, number>>({
  priority: 0,
  progressDate: 0,
  applicationDate: 0
})

// 日期筛选状态
const dateFilters = ref<Record<string, { startDate: string; endDate: string; active: boolean }>>({
  progressDate: { startDate: '', endDate: '', active: false },
  applicationDate: { startDate: '', endDate: '', active: false }
})

// 日期筛选弹窗显示状态
const dateFilterVisible = ref<Record<string, boolean>>({
  progressDate: false,
  applicationDate: false
})

// 临时日期筛选状态（用于弹窗编辑）
const tempDateFilter = ref<Record<string, { startDate: string; endDate: string }>>({
  progressDate: { startDate: '', endDate: '' },
  applicationDate: { startDate: '', endDate: '' }
})

// 临时日期范围状态（用于日期范围选择器）
const tempDateRangeFilter = ref<Record<string, [string, string] | []>>({
  progressDate: [],
  applicationDate: []
})

// 计算过滤和排序后的数据
const filteredAndSortedData = computed(() => {
  let data = [...store.applications]

  // 应用筛选
  Object.keys(filterValues.value).forEach(key => {
    const filterValue = filterValues.value[key]
    if (filterValue && filterValue.length > 0) {
      data = data.filter(item => {
        const cellValue = (item as any)[key]
        if (Array.isArray(filterValue)) {
          return filterValue.includes(cellValue)
        }
        return cellValue === filterValue
      })
    }
  })

  // 应用日期范围筛选
  Object.keys(dateFilters.value).forEach(key => {
    const filter = dateFilters.value[key]
    if (filter.active && (filter.startDate || filter.endDate)) {
      data = data.filter(item => {
        const cellValue = (item as any)[key]
        if (!cellValue) return false

        const itemDate = new Date(cellValue)
        let inRange = true

        if (filter.startDate) {
          const startDate = new Date(filter.startDate)
          inRange = inRange && itemDate >= startDate
        }

        if (filter.endDate) {
          const endDate = new Date(filter.endDate)
          inRange = inRange && itemDate <= endDate
        }

        return inRange
      })
    }
  })

  // 应用排序
  if (currentSort.value.sortBy) {
    const { sortBy, descending } = currentSort.value
    data.sort((a, b) => {
      const aValue = (a as any)[sortBy]
      const bValue = (b as any)[sortBy]

      if (sortBy === 'priority') {
        // 重视度排序：1(高) < 2(中) < 3(低)
        const aPriority = Number(aValue) || 999
        const bPriority = Number(bValue) || 999
        const result = aPriority - bPriority
        return descending ? -result : result
      } else if (sortBy === 'progressDate' || sortBy === 'applicationDate') {
        // 日期排序
        const aDate = aValue ? new Date(aValue).getTime() : 0
        const bDate = bValue ? new Date(bValue).getTime() : 0
        const result = aDate - bDate
        return descending ? -result : result
      }

      return 0
    })
  }

  return data
})



// 下拉选项
const priorityOptions = [
  { label: '高优先级', value: 1 },
  { label: '中优先级', value: 2 },
  { label: '低优先级', value: 3 }
]

const progressOptions = [
  { label: '已投递', value: '已投递' },
  { label: '测评', value: '测评' },
  { label: '笔试', value: '笔试' },
  { label: 'AI面', value: 'AI面' },
  { label: '一面', value: '一面' },
  { label: '二面', value: '二面' },
  { label: '三面', value: '三面' },
  { label: 'HR终面', value: 'HR终面' },
  { label: '谈offer', value: '谈offer' },
  { label: '签约', value: '签约' }
]

const statusOptions = [
  { label: '等消息', value: '等消息' },
  { label: '等我回复', value: '等我回复' },
  { label: '等待开始', value: '等待开始' },
  { label: '已过', value: '已过' },
  { label: '未过', value: '未过' },
  { label: '已放弃', value: '已放弃' },
  { label: '被调剂', value: '被调剂' },
  { label: '解约', value: '解约' }
]

// 表格列配置 - 优化为1200px窗口，备注列在可视区域外
const columns = ref([
  {
    colKey: 'row-select',
    type: 'multiple' as const,
    width: 50,
    fixed: 'left' as const
  },
  {
    colKey: 'company',
    title: '公司',
    width: 120,
    cell: 'company'
  },
  {
    colKey: 'applicationLink',
    title: '投递链接',
    width: 90,
    cell: 'applicationLink'
  },
  {
    colKey: 'priority',
    title: 'priority-title',
    width: 100,
    cell: 'priority'
  },
  {
    colKey: 'position',
    title: '职位',
    width: 100,
    cell: 'position'
  },
  {
    colKey: 'location',
    title: '地点',
    width: 90,
    cell: 'location'
  },
  {
    colKey: 'progress',
    title: '进展',
    width: 100,
    cell: 'progress',
    filter: {
      type: 'multiple' as const,
      resetValue: [],
      list: [
        { label: '已投递', value: '已投递' },
        { label: '测评', value: '测评' },
        { label: '笔试', value: '笔试' },
        { label: 'AI面', value: 'AI面' },
        { label: '一面', value: '一面' },
        { label: '二面', value: '二面' },
        { label: '三面', value: '三面' },
        { label: 'HR终面', value: 'HR终面' },
        { label: '谈offer', value: '谈offer' },
        { label: '签约', value: '签约' }
      ]
    }
  },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    cell: 'status',
    filter: {
      type: 'multiple' as const,
      resetValue: [],
      list: [
        { label: '等消息', value: '等消息' },
        { label: '等我回复', value: '等我回复' },
        { label: '等待开始', value: '等待开始' },
        { label: '已过', value: '已过' },
        { label: '未过', value: '未过' },
        { label: '已放弃', value: '已放弃' },
        { label: '被调剂', value: '被调剂' },
        { label: '解约', value: '解约' }
      ]
    }
  },
  {
    colKey: 'progressDate',
    title: 'progressDate-title',
    width: 130,
    cell: 'progressDate'
  },
  {
    colKey: 'applicationDate',
    title: 'applicationDate-title',
    width: 130,
    cell: 'applicationDate'
  },
  {
    colKey: 'notes',
    title: '备注',
    width: 150,
    cell: 'notes'
  },
  {
    colKey: 'referralCode',
    title: '内推码',
    width: 100,
    cell: 'referralCode'
  },

])

// 方法
const getPriorityTheme = (priority: number) => {
  switch (priority) {
    case 1: return 'danger'
    case 2: return 'warning'
    case 3: return 'success'
    default: return 'default'
  }
}

const getPriorityText = (priority: number) => {
  switch (priority) {
    case 1: return '高'
    case 2: return '中'
    case 3: return '低'
    default: return '未设置'
  }
}

const startEdit = (rowIndex: number, column: string, value: any) => {
  editingCell.rowIndex = rowIndex
  editingCell.column = column
  editingValue.value = value || ''
}

const saveEdit = async (rowIndex: number, column: string) => {
  if (editingCell.rowIndex === rowIndex && editingCell.column === column) {
    const application = filteredAndSortedData.value[rowIndex]
    if (application && application.id) {
      try {
        await store.updateApplication(application.id, {
          [column]: editingValue.value
        })
        cancelEdit()
        MessagePlugin.success('保存成功')
      } catch (error) {
        MessagePlugin.error('保存失败')
        console.error('保存失败:', error)
      }
    }
  }
}

// 防止重复调用的标志
let isProcessingDateChange = false

// 处理日期选择器的变化
const handleDateChangeWithParams = async (rowIndex: number, column: string, value: any) => {
  // 防止重复调用
  if (!value || isProcessingDateChange) {
    return
  }

  // 设置处理标志
  isProcessingDateChange = true

  const application = filteredAndSortedData.value[rowIndex]

  if (application) {
    // 转换日期值为字符串格式，修复时区问题
    let dateString = ''
    if (value) {
      if (typeof value === 'string') {
        // 如果已经是字符串格式，直接使用
        dateString = value
      } else if (value instanceof Date) {
        // 修复时区问题：使用本地时间而不是UTC时间
        const year = value.getFullYear()
        const month = String(value.getMonth() + 1).padStart(2, '0')
        const day = String(value.getDate()).padStart(2, '0')
        dateString = `${year}-${month}-${day}`
      } else if (value && value.toString) {
        dateString = value.toString()
      } else {
        dateString = String(value)
      }
    }

    if (application.id) {
      try {
        await store.updateApplication(application.id, {
          [column]: dateString
        })
        cancelEdit()
        MessagePlugin.success('日期保存成功')
      } catch (error) {
        MessagePlugin.error('保存失败')
        console.error('保存失败:', error)
      }
    } else {
      MessagePlugin.error('记录ID无效')
    }
  }

  // 延迟重置处理标志，防止快速重复调用
  setTimeout(() => {
    isProcessingDateChange = false
  }, 100)
}



const cancelEdit = () => {
  editingCell.rowIndex = -1
  editingCell.column = ''
  editingValue.value = ''
}

// 处理键盘事件
const handleKeyup = (event: any, rowIndex: number, column: string) => {
  if (event && event.key === 'Enter') {
    saveEdit(rowIndex, column)
  } else if (event && event.key === 'Escape') {
    cancelEdit()
  }
}

const addNewRow = async () => {
  const username = currentUsername.value.trim()
  if (!username) {
    MessagePlugin.warning('请先输入用户名')
    return
  }

  // 验证用户名格式
  const validation = userService.validateUsername(username)
  if (!validation.valid) {
    MessagePlugin.warning(validation.message)
    return
  }

  // 简化用户处理：直接创建记录，不需要复杂的用户管理
  let user = userService.findUserByUsername(username)
  if (!user) {
    // 自动创建本地用户记录
    try {
      user = await userService.createUser(username)
      userService.setCurrentUser(user)
      loadUsers()
      console.log(`已创建本地用户: ${username}`)
    } catch (error) {
      console.warn(`创建本地用户失败: ${error}，继续使用临时用户`)
      // 即使本地用户创建失败，也可以继续，使用临时用户ID
      user = {
        id: Date.now(),
        username: username,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    }
  }

  const newRow = {
    userId: user!.id,
    username: username,
    company: '',
    applicationLink: '',
    priority: 2,
    industry: '',
    tags: '',
    position: '',
    location: '',
    progress: '',
    status: '',
    progressDate: '',
    applicationDate: '',
    notes: '',
    referralCode: ''
  }

  try {
    await store.addApplication(newRow)
    MessagePlugin.success('添加新记录成功')
  } catch (error) {
    MessagePlugin.error(`添加记录失败: ${error}`)
  }
}







// 验证URL是否有效
const isValidUrl = (url: string) => {
  if (!url || url.trim() === '') return false
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

// 在默认浏览器中打开链接
const openLink = (url: string) => {
  if (!isValidUrl(url)) {
    MessagePlugin.warning('无效的链接地址')
    return
  }

  // 在Electron环境中使用shell.openExternal
  if ((window as any).electronAPI && (window as any).electronAPI.openExternal) {
    (window as any).electronAPI.openExternal(url)
  } else {
    // 在浏览器环境中使用window.open
    window.open(url, '_blank')
  }
}

// 多选相关方法
const onSelectChange = (selectedKeys: (string | number)[]) => {
  selectedRowKeys.value = selectedKeys
}



// 筛选变化处理
const onFilterChange = (filterInfo: any) => {
  filterValues.value = { ...filterInfo }
}

// 列拖拽排序处理
const onColumnDragSort = (params: any) => {
  if (params && params.newData) {
    columns.value = params.newData
  }
}

// 切换排序状态
const toggleSort = (column: string) => {
  // 重置其他列的排序状态
  Object.keys(sortStates.value).forEach(key => {
    if (key !== column) {
      sortStates.value[key] = 0
    }
  })

  // 切换当前列的排序状态：0 -> 1 -> 2 -> 0
  sortStates.value[column] = (sortStates.value[column] + 1) % 3

  // 更新排序配置
  if (sortStates.value[column] === 0) {
    // 无排序
    currentSort.value = {}
  } else {
    // 有排序
    currentSort.value = {
      sortBy: column,
      descending: sortStates.value[column] === 2 // 2=降序
    }
  }
}

// 获取排序图标
const getSortIcon = (column: string) => {
  const state = sortStates.value[column]
  switch (state) {
    case 0: return SwapIcon // 无排序 - 菱形图标
    case 1: return ArrowUpIcon // 升序
    case 2: return ArrowDownIcon // 降序
    default: return SwapIcon
  }
}

// 获取排序提示文字
const getSortTitle = (column: string) => {
  const state = sortStates.value[column]
  const columnNames: Record<string, string> = {
    priority: '重视度',
    progressDate: '进展时间',
    applicationDate: '投递时间'
  }
  const name = columnNames[column] || column

  switch (state) {
    case 0: return `点击按${name}升序排序`
    case 1: return `点击按${name}降序排序`
    case 2: return `点击取消${name}排序`
    default: return `排序${name}`
  }
}

// 切换日期筛选
const toggleDateFilter = (column: string) => {
  // 打开弹窗时，初始化临时状态
  if (!dateFilterVisible.value[column]) {
    const filter = dateFilters.value[column]
    tempDateFilter.value[column] = {
      startDate: filter.startDate,
      endDate: filter.endDate
    }

    // 初始化日期范围选择器状态
    if (filter.startDate && filter.endDate) {
      tempDateRangeFilter.value[column] = [filter.startDate, filter.endDate]
    } else if (filter.startDate) {
      tempDateRangeFilter.value[column] = [filter.startDate, '']
    } else if (filter.endDate) {
      tempDateRangeFilter.value[column] = ['', filter.endDate]
    } else {
      tempDateRangeFilter.value[column] = []
    }
  }
  dateFilterVisible.value[column] = !dateFilterVisible.value[column]
}

// 获取筛选提示文字
const getFilterTitle = (column: string) => {
  const filter = dateFilters.value[column]
  const columnNames: Record<string, string> = {
    progressDate: '进展时间',
    applicationDate: '投递时间'
  }
  const name = columnNames[column] || column

  if (filter.active) {
    return `点击修改${name}筛选条件`
  }
  return `点击设置${name}筛选条件`
}

// 检查日期筛选是否激活
const isDateFilterActive = (column: string) => {
  return dateFilters.value[column].active
}

// 应用日期筛选
const applyDateFilter = (column: string, startDate: string, endDate: string) => {
  dateFilters.value[column] = {
    startDate,
    endDate,
    active: !!(startDate || endDate)
  }
  dateFilterVisible.value[column] = false
}

// 清除日期筛选
const clearDateFilter = (column: string) => {
  dateFilters.value[column] = {
    startDate: '',
    endDate: '',
    active: false
  }
  tempDateFilter.value[column] = {
    startDate: '',
    endDate: ''
  }
  tempDateRangeFilter.value[column] = []
  dateFilterVisible.value[column] = false
}

// 处理日期筛选确认
const handleDateFilterConfirm = (column: string) => {
  const rangeValue = tempDateRangeFilter.value[column]
  let startDate = ''
  let endDate = ''

  if (Array.isArray(rangeValue) && rangeValue.length === 2) {
    startDate = rangeValue[0] || ''
    endDate = rangeValue[1] || ''
  }

  applyDateFilter(column, startDate, endDate)
}

// 处理日期筛选取消
const handleDateFilterCancel = (column: string) => {
  dateFilterVisible.value[column] = false
}

// 批量删除选中的记录
const batchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要删除的记录')
    return
  }

  const count = selectedRowKeys.value.length

  // 使用确认对话框
  if (confirm(`确定要删除选中的 ${count} 条记录吗？此操作不可恢复！`)) {
    try {
      // 批量删除（异步）
      await Promise.all(
        selectedRowKeys.value.map(id => store.deleteApplication(Number(id)))
      )

      // 清空选择
      selectedRowKeys.value = []
      MessagePlugin.success(`成功删除 ${count} 条记录`)
    } catch (error) {
      MessagePlugin.error(`删除失败: ${error}`)
    }
  }
}



// 用户管理方法
const loadUsers = () => {
  const users = userService.getAllUsers()
  userOptions.value = users.map(user => ({
    label: user.username,
    value: user.username
  }))

  // 设置当前用户名
  const currentUser = userService.getCurrentUser()
  if (currentUser) {
    currentUsername.value = currentUser.username
  } else if (users.length > 0) {
    currentUsername.value = users[0].username
    userService.setCurrentUser(users[0])
  }
}

// 管理员模式下的用户切换
const onAdminUserChange = (value: any) => {
  const username = String(value || '')
  if (!username) {
    currentUsername.value = ''
    return
  }

  const user = userService.findUserByUsername(username)
  if (user) {
    userService.setCurrentUser(user)
    currentUsername.value = username
    // 重新加载该用户的数据
    loadUserJobApplications()
    MessagePlugin.info(`已切换到用户: ${username}`)
  }
}

// 普通用户模式下的用户名变更
const onUsernameChange = () => {
  const username = currentUsername.value.trim()
  if (!username) return

  // 验证用户名格式
  const validation = userService.validateUsername(username)
  if (!validation.valid) {
    MessagePlugin.warning(validation.message)
    return
  }

  // 查找或创建用户
  let user = userService.findUserByUsername(username)
  if (!user) {
    // 在普通模式下，如果用户不存在，暂时不创建，等同步时再创建
    MessagePlugin.info(`将使用用户名: ${username}`)
  } else {
    userService.setCurrentUser(user)
    loadUserJobApplications()
    MessagePlugin.info(`已切换到用户: ${username}`)
  }
}

const validateNewUsername = () => {
  usernameValidation.value = userService.validateUsername(newUsername.value)
}

const createNewUser = async () => {
  try {
    if (!usernameValidation.value.valid) {
      MessagePlugin.error(usernameValidation.value.message)
      return
    }

    const newUser = await userService.createUser(newUsername.value)
    loadUsers()
    currentUsername.value = newUser.username
    userService.setCurrentUser(newUser)

    showCreateUserDialog.value = false
    newUsername.value = ''
    usernameValidation.value = { valid: false, message: '' }

    MessagePlugin.success(`用户 ${newUser.username} 创建成功`)
  } catch (error) {
    MessagePlugin.error(`创建用户失败: ${error}`)
  }
}

const cancelCreateUser = () => {
  showCreateUserDialog.value = false
  newUsername.value = ''
  usernameValidation.value = { valid: false, message: '' }
}



const loadUserJobApplications = async () => {
  const username = currentUsername.value.trim()
  if (username) {
    // 根据用户名加载特定用户的数据
    const user = userService.findUserByUsername(username)
    if (user) {
      userService.setCurrentUser(user)
    }
    await store.loadFromStorage()
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  // 初始化管理员服务
  adminService.initKeyboardListener()
  adminService.setMessageHandler((message, type) => {
    if (type === 'success') {
      MessagePlugin.success(message)
    } else if (type === 'error') {
      MessagePlugin.error(message)
    } else {
      MessagePlugin.info(message)
    }
  })

  // 初始化用户服务
  await userService.initializeDefaultUser()
  loadUsers()



  // 加载数据
  await store.loadFromStorage()
})
</script>

<style scoped>
.job-application-table {
  padding: 15px;
  height: 100%; /* 使用父容器的全部高度 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-shrink: 0;
}

.table-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.user-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e6e8eb;
  min-width: 280px;
}

.user-selector label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  margin: 0;
  font-weight: 500;
}

.sync-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-actions .t-checkbox {
  font-size: 14px;
  color: #666;
}

.table-wrapper {
  height: 800px;
  width: 100%;
  overflow-y: auto;
  overflow-x: auto;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  /* 为表格容器添加自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 自定义滚动条样式 - 垂直和水平滚动条 */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.table-wrapper::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

.editable-cell {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: block;
  min-height: 18px;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.editable-cell:hover {
  background-color: #f5f5f5;
}

.priority-tag {
  cursor: pointer;
  font-size: 12px;
}

.link-cell {
  position: relative;
}

.link-cell.has-link {
  color: #1890ff;
  text-decoration: underline;
}

.link-cell.has-link:hover {
  color: #40a9ff;
  background-color: #e6f7ff;
}

/* 固定窗口大小优化 */
:deep(.t-table) {
  font-size: 12px;
  border: none;
}

:deep(.t-table__header) {
  font-size: 12px;
}

:deep(.t-table__body) {
  font-size: 12px;
}

:deep(.t-input) {
  font-size: 12px;
}

:deep(.t-select) {
  font-size: 12px;
}

:deep(.t-date-picker) {
  font-size: 12px;
}

/* 自定义选择器弹出层样式 */
:deep(.custom-select-popup) {
  font-size: 12px;
}

/* 隐藏TDesign内部组件的size属性警告 */
:deep(.t-select-panel) {
  font-size: inherit;
}

/* 修复TDesign Select组件的size属性传递问题 */
:deep(.t-select-panel[size]) {
  font-size: 12px;
}

:deep(.t-popup__content .t-select-panel) {
  font-size: 12px;
}

/* 自定义列标题样式 */
.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-buttons {
  display: flex;
  align-items: center;
  gap: 2px;
}

.sort-button,
.filter-button {
  margin-left: 2px;
  padding: 2px !important;
  min-width: 20px !important;
  height: 20px !important;
}

.sort-button :deep(.t-icon),
.filter-button :deep(.t-icon) {
  font-size: 14px;
}

/* 按钮悬浮效果 */
.sort-button:hover,
.filter-button:hover {
  background-color: var(--td-bg-color-container-hover);
}

/* 激活状态的筛选按钮 */
.filter-button.filter-active {
  background-color: var(--td-brand-color);
  color: white;
}

.filter-button.filter-active:hover {
  background-color: var(--td-brand-color-hover);
}

/* 日期筛选弹窗样式 */
.date-filter-content {
  padding: 16px 0;
}

.date-range-section {
  margin-bottom: 20px;
}

.date-range-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.date-filter-tips {
  background-color: var(--td-bg-color-container-select);
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
}

.date-filter-tips p {
  margin: 4px 0;
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

.date-filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.date-filter-row label {
  width: 80px;
  margin-right: 12px;
  font-weight: 500;
}

/* 表格容器样式 - 简化版本 */
:deep(.t-table) {
  width: 100%;
  min-width: 1260px;
  border: none;
}

/* 固定表头设置 */
:deep(.t-table__header) {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格内容区域 */
:deep(.t-table__content) {
  height: 780px;
  overflow-y: auto;
  overflow-x: auto;
}

/* 表格主体可滚动 */
:deep(.t-table__body) {
  height: auto;
  max-height: calc(780px - 60px); /* 减去表头高度 */
}

:deep(.t-table__body-container) {
  overflow: visible;
}

/* 禁用可能的虚拟滚动 */
:deep(.t-table__virtual-scroll) {
  display: none !important;
}

/* 确保所有行都能正常渲染 */
:deep(.t-table__body .t-table__row) {
  display: table-row !important;
  visibility: visible !important;
}

/* 移除分页组件的滚动条 */
:deep(.t-pagination) {
  overflow: visible !important;
}

/* 表格单元格内容处理 */
:deep(.t-table__cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 12px; /* 增加内边距提升可读性 */
}

/* 特殊列的样式优化 */
:deep(.t-table__cell[data-colkey="company"]) {
  font-weight: 500; /* 公司名称加粗 */
}

:deep(.t-table__cell[data-colkey="position"]) {
  font-weight: 500; /* 职位名称加粗 */
}

:deep(.t-table__cell[data-colkey="priority"]) {
  text-align: center; /* 重视度居中显示 */
}



/* 移除选中行高亮样式 */

/* 悬浮效果增强 */
:deep(.t-table__row:hover) {
  background-color: #f5f5f5;
  transition: background-color 0.2s ease;
}

/* 选择列样式 */
:deep(.t-table__cell[data-colkey="row-select"]) {
  text-align: center;
  padding: 8px 4px;
}

/* 移除所有按钮的焦点边框 */
:deep(.t-button) {
  outline: none !important;
}

:deep(.t-button:focus) {
  outline: none !important;
  box-shadow: none !important;
}

/* 空数据状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120px 40px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  margin: 20px;
  border: 2px dashed #e2e8f0;
  transition: all 0.3s ease;
  min-height: 450px;
}

.empty-state:hover {
  border-color: #cbd5e1;
  background: white;
}

.empty-icon {
  margin-bottom: 24px;
  color: #64748b;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

.empty-icon svg {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 12px;
  letter-spacing: -0.025em;
}

.empty-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  max-width: 400px;
  margin-bottom: 32px;
}

.empty-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.empty-actions .t-button {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.empty-actions .t-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .empty-state {
    padding: 60px 20px;
    margin: 10px;
  }

  .empty-title {
    font-size: 18px;
  }

  .empty-description {
    font-size: 13px;
    max-width: 300px;
  }

  .empty-actions {
    flex-direction: column;
    align-items: center;
  }

  .empty-actions .t-button {
    width: 100%;
    max-width: 200px;
  }
}

/* 创建用户对话框样式 */
.create-user-form {
  padding: 16px 0;
}

.create-user-form .form-group {
  margin-bottom: 16px;
}

.create-user-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.create-user-form .t-input {
  width: 100%;
}

/* 用户选择器响应式 */
@media (max-width: 1200px) {
  .header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .user-selector {
    width: 100%;
    justify-content: space-between;
  }

  .sync-actions,
  .table-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
