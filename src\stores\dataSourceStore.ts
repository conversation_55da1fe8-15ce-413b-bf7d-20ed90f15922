import { defineStore } from 'pinia'
import { ref } from 'vue'

export type DataSourceType = 'mysql' | 'feishu'

export const useDataSourceStore = defineStore('dataSource', () => {
  // 当前选择的数据源
  const currentDataSource = ref<DataSourceType>('mysql')
  
  // 设置数据源
  const setDataSource = (source: DataSourceType) => {
    currentDataSource.value = source
    saveToStorage()
  }
  
  // 保存到本地存储
  const saveToStorage = () => {
    try {
      localStorage.setItem('data-source-type', currentDataSource.value)
    } catch (error) {
      console.error('保存数据源配置失败:', error)
    }
  }
  
  // 从本地存储加载
  const loadFromStorage = () => {
    try {
      const saved = localStorage.getItem('data-source-type')
      if (saved && (saved === 'mysql' || saved === 'feishu')) {
        currentDataSource.value = saved as DataSourceType
      }
    } catch (error) {
      console.error('加载数据源配置失败:', error)
    }
  }
  
  // 初始化时加载配置
  loadFromStorage()

  // 确保有默认的MySQL配置
  const initializeDefaultConfigs = () => {
    try {
      // 检查是否已有MySQL配置
      const mysqlConfig = localStorage.getItem('mysql-config')
      if (!mysqlConfig) {
        // 设置默认MySQL配置
        const defaultConfig = {
          host: 'mysql5.sqlpub.com',
          port: 3310,
          user: 'fish2609',
          password: 'Hrk6OCOXYo39ToeS',
          database: 'jobfish_pool'
        }
        localStorage.setItem('mysql-config', JSON.stringify(defaultConfig))
        console.log('已设置默认MySQL配置')
      }
    } catch (error) {
      console.error('初始化默认配置失败:', error)
    }
  }

  // 初始化默认配置
  initializeDefaultConfigs()

  return {
    currentDataSource,
    setDataSource,
    loadFromStorage
  }
})
