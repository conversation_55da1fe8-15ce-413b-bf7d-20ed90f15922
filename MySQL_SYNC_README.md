# MySQL数据库同步功能使用说明

## 功能概述

本系统现已支持求职申请数据的MySQL数据库同步功能，实现了多用户数据管理和本地数据库与MySQL数据库的双向同步。

## 主要功能

### 1. 多用户管理
- **用户创建**: 支持创建多个用户账户
- **用户切换**: 可以在不同用户之间切换
- **数据隔离**: 每个用户的求职申请数据独立存储

### 2. MySQL数据库同步
- **一键上传**: 将本地数据同步到MySQL数据库
- **一键下载**: 从MySQL数据库下载数据到本地
- **双向同步**: 支持本地和远程数据的双向同步

### 3. 数据库表结构

#### userdata表
```sql
CREATE TABLE userdata (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### job_applications表
```sql
CREATE TABLE job_applications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  username VARCHAR(255) NOT NULL,
  company VARCHAR(255) NOT NULL DEFAULT '',
  applicationLink TEXT DEFAULT '',
  priority INT DEFAULT 2,
  industry VARCHAR(255) DEFAULT '',
  tags TEXT DEFAULT '',
  position VARCHAR(255) DEFAULT '',
  location VARCHAR(255) DEFAULT '',
  progress VARCHAR(100) DEFAULT '',
  status VARCHAR(100) DEFAULT '',
  progressDate DATE NULL,
  applicationDate DATE NULL,
  notes TEXT DEFAULT '',
  referralCode VARCHAR(255) DEFAULT '',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES userdata(id) ON DELETE CASCADE
);
```

## 使用步骤

### 1. 配置MySQL数据库

1. **进入管理员模式**
   - 按 `Ctrl + P` 输入管理员密码进入管理员面板

2. **配置MySQL连接**
   - 在管理员面板中选择"MySQL数据库"
   - 填写MySQL数据库连接信息：
     - 主机地址
     - 端口号
     - 用户名
     - 密码
     - 数据库名

3. **创建数据库表**
   - 点击"创建数据库表"按钮
   - 系统会自动创建所需的表结构

4. **测试连接**
   - 点击"测试连接"按钮确认数据库连接正常

### 2. 用户管理

1. **创建用户**
   - 在管理员面板的"用户管理"部分
   - 输入用户名，点击"创建用户"
   - 用户名要求：2-50个字符，支持中文、英文、数字、下划线和连字符

2. **切换用户**
   - 在求职申请表页面顶部的用户选择器中选择用户
   - 或在管理员面板中点击"设为当前"

3. **删除用户**
   - 在管理员面板中点击用户旁边的"删除"按钮
   - 注意：删除用户会同时删除该用户的所有数据

### 3. 数据同步

1. **上传数据到MySQL**
   - 在求职申请表页面点击"一键同步"按钮
   - 系统会将当前用户的本地数据上传到MySQL数据库

2. **从MySQL下载数据**
   - 点击"下载数据"按钮
   - 系统会从MySQL数据库下载当前用户的数据到本地
   - 注意：下载会覆盖本地数据

3. **同步状态指示**
   - 绿色按钮：MySQL连接正常，可以进行同步
   - 灰色按钮：MySQL连接异常或未选择用户

## 数据流程

```
本地数据库 (IndexedDB) ←→ MySQL数据库
        ↑                    ↑
        └── 用户界面操作 ──────┘
```

1. **本地存储**: 使用IndexedDB存储本地数据
2. **用户管理**: 本地用户信息存储在localStorage中
3. **MySQL同步**: 通过Tauri后端与MySQL数据库通信
4. **数据隔离**: 每个用户的数据独立存储和同步

## 注意事项

### 安全性
- 管理员密码：`sFISHzpy1!`
- MySQL连接信息会保存在本地存储中
- 建议定期备份重要数据

### 数据一致性
- 同步操作会覆盖目标端的数据
- 建议在同步前备份重要数据
- 多设备使用时注意数据冲突

### 性能考虑
- 大量数据同步可能需要较长时间
- 网络连接质量会影响同步速度
- 建议在网络稳定时进行同步操作

## 故障排除

### 常见问题

1. **MySQL连接失败**
   - 检查网络连接
   - 确认MySQL服务器配置
   - 验证用户名和密码

2. **同步失败**
   - 检查MySQL连接状态
   - 确认用户已选择
   - 查看错误提示信息

3. **数据丢失**
   - 检查本地存储空间
   - 确认MySQL表结构正确
   - 查看浏览器控制台错误信息

### 技术支持

如遇到技术问题，请检查：
1. 浏览器控制台错误信息
2. MySQL数据库日志
3. 网络连接状态
4. 本地存储空间

## 更新日志

### v1.0.0
- 实现基础的MySQL同步功能
- 支持多用户管理
- 添加管理员面板配置界面
- 实现数据库表自动创建功能
