import { useDataSourceStore, type DataSourceType } from '../stores/dataSourceStore'
import { createMySQLClient, validateMySQLConfig, loadMySQLConfigFromStorage, defaultMySQLConfig } from './mysqlApi'
import { 
  createFeishuClient, 
  validateConfig, 
  loadConfigFromStorage,
  convertFeishuRecord,
  type FieldMapping 
} from './feishuApi'

export interface DataSyncResult {
  success: boolean
  count: number
  data: any[]
  error?: string
}

/**
 * 通用数据同步服务
 */
export class DataSyncService {
  private getDataSourceStore() {
    return useDataSourceStore()
  }

  /**
   * 获取热门秋招汇总数据
   */
  async getHotAutumnData(): Promise<DataSyncResult> {
    const dataSource = this.getDataSourceStore().currentDataSource
    
    try {
      if (dataSource === 'mysql') {
        return await this.getDataFromMySQL('autumn')
      } else {
        return await this.getDataFromFeishu('autumn')
      }
    } catch (error) {
      console.error('获取热门秋招汇总数据失败:', error)
      return {
        success: false,
        count: 0,
        data: [],
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 获取热门实习汇总数据
   */
  async getHotInternshipData(): Promise<DataSyncResult> {
    const dataSource = this.getDataSourceStore().currentDataSource
    
    try {
      if (dataSource === 'mysql') {
        return await this.getDataFromMySQL('internship')
      } else {
        return await this.getDataFromFeishu('internship')
      }
    } catch (error) {
      console.error('获取热门实习汇总数据失败:', error)
      return {
        success: false,
        count: 0,
        data: [],
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 获取国企央企汇总数据
   */
  async getStateOwnedData(): Promise<DataSyncResult> {
    const dataSource = this.getDataSourceStore().currentDataSource
    
    try {
      if (dataSource === 'mysql') {
        return await this.getDataFromMySQL('stateOwned')
      } else {
        return await this.getDataFromFeishu('stateOwned')
      }
    } catch (error) {
      console.error('获取国企央企汇总数据失败:', error)
      return {
        success: false,
        count: 0,
        data: [],
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 从MySQL获取数据
   */
  private async getDataFromMySQL(dataType: 'autumn' | 'internship' | 'stateOwned'): Promise<DataSyncResult> {
    const config = loadMySQLConfigFromStorage()
    if (!config || !validateMySQLConfig(config)) {
      throw new Error('MySQL配置不完整或无效')
    }

    const client = createMySQLClient(config)

    try {
      let records: any[] = []
      
      switch (dataType) {
        case 'autumn':
          records = await client.getHotAutumnData()
          break
        case 'internship':
          records = await client.getHotInternshipData()
          break
        case 'stateOwned':
          records = await client.getStateOwnedData()
          break
      }

      // 转换数据格式
      const convertedRecords = records.map((record, index) => ({
        id: record.id || Date.now() + index,
        updateTime: record.updateTime || '',
        company: record.company || '',
        applicationLink: record.applicationLink || '',
        industry: record.industry || '',
        tags: record.tags || '',
        batch: record.batch || '',
        isHot: String(record.isHot || ''),
        position: record.position || '',
        location: record.location || '',
        deadline: record.deadline || ''
      }))

      return {
        success: true,
        count: convertedRecords.length,
        data: convertedRecords
      }
    } finally {
      await client.close()
    }
  }

  /**
   * 从飞书获取数据
   */
  private async getDataFromFeishu(dataType: 'autumn' | 'internship' | 'stateOwned'): Promise<DataSyncResult> {
    const config = loadConfigFromStorage()
    if (!config || !validateConfig(config)) {
      throw new Error('飞书配置不完整或无效')
    }

    let tableId: string
    switch (dataType) {
      case 'autumn':
        tableId = config.tableId
        break
      case 'internship':
        tableId = config.internshipTableId
        break
      case 'stateOwned':
        tableId = config.stateOwnedTableId
        break
    }

    const client = createFeishuClient({
      ...config,
      tableId: tableId
    })

    // 获取所有记录
    const records = await client.getAllRecords()

    if (!records || !Array.isArray(records)) {
      throw new Error('获取到的数据格式不正确或为空')
    }

    // 使用默认字段映射
    const mapping: FieldMapping = {
      updateTime: '更新',
      company: '公司',
      applicationLink: '投递链接',
      industry: '行业',
      tags: '标签',
      batch: '批次',
      isHot: '热门',
      position: '职位',
      location: '地点',
      deadline: '投递截至'
    }

    // 转换数据格式
    const convertedRecords = records.map((record, index) =>
      convertFeishuRecord(record, mapping, index)
    )

    return {
      success: true,
      count: convertedRecords.length,
      data: convertedRecords
    }
  }

  /**
   * 获取当前数据源类型
   */
  getCurrentDataSource(): DataSourceType {
    return this.getDataSourceStore().currentDataSource
  }

  /**
   * 检查当前数据源配置是否有效
   */
  isCurrentDataSourceConfigValid(): boolean {
    const dataSource = this.getDataSourceStore().currentDataSource

    if (dataSource === 'mysql') {
      const config = loadMySQLConfigFromStorage()
      if (config && validateMySQLConfig(config)) {
        return true
      }
      // 如果没有保存的配置，检查是否有默认配置
      return validateMySQLConfig(defaultMySQLConfig)
    } else {
      const config = loadConfigFromStorage()
      if (config && validateConfig(config)) {
        return true
      }
      // 飞书配置需要用户手动配置，没有有效的默认配置
      return false
    }
  }
}

// 创建单例实例（延迟初始化）
let _dataSyncService: DataSyncService | null = null

export function getDataSyncService(): DataSyncService {
  if (!_dataSyncService) {
    _dataSyncService = new DataSyncService()
  }
  return _dataSyncService
}

// 为了向后兼容，保留原有的导出方式
export const dataSyncService = {
  getHotAutumnData: () => getDataSyncService().getHotAutumnData(),
  getHotInternshipData: () => getDataSyncService().getHotInternshipData(),
  getStateOwnedData: () => getDataSyncService().getStateOwnedData(),
  getCurrentDataSource: () => getDataSyncService().getCurrentDataSource(),
  isCurrentDataSourceConfigValid: () => getDataSyncService().isCurrentDataSourceConfigValid()
}
