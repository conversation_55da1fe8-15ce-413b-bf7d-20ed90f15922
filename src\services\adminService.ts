// 管理员权限管理服务
import { ref, computed } from 'vue'

// 管理员密码
const ADMIN_PASSWORD = 'sFISHzpy1!'

// 管理员状态
const isAdminMode = ref(false)
const isAdminAuthenticated = ref(false)

// 从sessionStorage恢复状态
const restoreAdminState = () => {
  try {
    const savedAuth = sessionStorage.getItem('admin_authenticated')
    const savedMode = sessionStorage.getItem('admin_mode')
    
    if (savedAuth === 'true') {
      isAdminAuthenticated.value = true
    }
    
    if (savedMode === 'true') {
      isAdminMode.value = true
    }
  } catch (error) {
    console.error('恢复管理员状态失败:', error)
  }
}

// 保存状态到sessionStorage
const saveAdminState = () => {
  try {
    sessionStorage.setItem('admin_authenticated', isAdminAuthenticated.value.toString())
    sessionStorage.setItem('admin_mode', isAdminMode.value.toString())
  } catch (error) {
    console.error('保存管理员状态失败:', error)
  }
}

// 管理员服务类
export class AdminService {
  private static instance: AdminService

  private constructor() {
    // 恢复状态
    restoreAdminState()
  }

  static getInstance(): AdminService {
    if (!AdminService.instance) {
      AdminService.instance = new AdminService()
    }
    return AdminService.instance
  }

  /**
   * 获取管理员模式状态
   */
  get isAdminMode() {
    return computed(() => isAdminMode.value)
  }

  /**
   * 获取管理员认证状态
   */
  get isAdminAuthenticated() {
    return computed(() => isAdminAuthenticated.value)
  }

  /**
   * 验证管理员密码
   */
  validatePassword(password: string): boolean {
    return password === ADMIN_PASSWORD
  }

  /**
   * 设置管理员认证状态
   */
  setAuthenticated(authenticated: boolean): void {
    isAdminAuthenticated.value = authenticated
    if (!authenticated) {
      isAdminMode.value = false
    }
    saveAdminState()
  }

  /**
   * 设置管理员模式
   */
  setAdminMode(mode: boolean): void {
    if (mode && !isAdminAuthenticated.value) {
      throw new Error('未认证的管理员无法进入管理员模式')
    }
    isAdminMode.value = mode
    saveAdminState()
  }

  /**
   * 切换管理员模式
   */
  toggleAdminMode(): boolean {
    if (!isAdminAuthenticated.value) {
      return false
    }
    isAdminMode.value = !isAdminMode.value
    saveAdminState()
    return isAdminMode.value
  }

  /**
   * 管理员登录
   */
  login(password: string): boolean {
    if (this.validatePassword(password)) {
      this.setAuthenticated(true)
      this.setAdminMode(true)
      return true
    }
    return false
  }

  /**
   * 管理员登出
   */
  logout(): void {
    this.setAuthenticated(false)
    this.setAdminMode(false)
  }

  /**
   * 检查是否有管理员权限
   */
  hasAdminPermission(): boolean {
    return isAdminAuthenticated.value
  }

  /**
   * 检查是否在管理员模式
   */
  isInAdminMode(): boolean {
    return isAdminMode.value
  }

  /**
   * 初始化键盘事件监听
   */
  initKeyboardListener(): void {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'p') {
        event.preventDefault()
        this.handleAdminToggle()
      }
    }

    // 移除旧的监听器（如果存在）
    document.removeEventListener('keydown', handleKeyDown)
    // 添加新的监听器
    document.addEventListener('keydown', handleKeyDown)
  }

  /**
   * 处理管理员模式切换
   */
  private handleAdminToggle(): void {
    if (isAdminAuthenticated.value) {
      // 已认证，直接切换模式
      const newMode = this.toggleAdminMode()
      const message = newMode ? '已进入管理员模式' : '已退出管理员模式'
      this.showMessage(message, newMode ? 'success' : 'info')
    } else {
      // 未认证，需要输入密码
      const password = prompt('请输入管理员密码:')
      if (password !== null) {
        if (this.login(password)) {
          this.showMessage('管理员认证成功，已进入管理员模式', 'success')
        } else {
          this.showMessage('密码错误', 'error')
        }
      }
    }
  }

  /**
   * 显示消息（需要在组件中重写）
   */
  private showMessage(message: string, type: 'success' | 'error' | 'info'): void {
    console.log(`[${type.toUpperCase()}] ${message}`)
    // 这个方法会在组件中被重写以使用实际的消息组件
  }

  /**
   * 设置消息显示函数
   */
  setMessageHandler(handler: (message: string, type: 'success' | 'error' | 'info') => void): void {
    this.showMessage = handler
  }
}

// 导出单例实例
export const adminService = AdminService.getInstance()

// 导出响应式状态供组件使用
export const useAdminState = () => {
  return {
    isAdminMode: adminService.isAdminMode,
    isAdminAuthenticated: adminService.isAdminAuthenticated,
    hasAdminPermission: () => adminService.hasAdminPermission(),
    isInAdminMode: () => adminService.isInAdminMode(),
    login: (password: string) => adminService.login(password),
    logout: () => adminService.logout(),
    toggleAdminMode: () => adminService.toggleAdminMode(),
    setAdminMode: (mode: boolean) => adminService.setAdminMode(mode)
  }
}
