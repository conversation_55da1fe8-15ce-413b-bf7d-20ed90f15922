// MySQL数据库API服务

export interface MySQLConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
}

export interface MySQLRecord {
  id?: number
  updateTime?: string
  company: string
  applicationLink: string
  industry: string
  tags: string
  batch: string
  isHot: string
  position: string
  location: string
  deadline: string
}

// 用户数据接口
export interface MySQLUser {
  id?: number
  username: string
  created_at?: string
  updated_at?: string
}

// 求职申请数据接口（MySQL版本 - 适配现有userdata表）
export interface MySQLJobApplication {
  id?: number
  username: string
  公司: string
  投递链接: string
  重视度: number
  职位: string
  地点: string
  进展: string
  状态: string
  进展时间: string
  投递时间: string
  备注: string
  内推码: string
}

/**
 * MySQL数据库API客户端
 */
export class MySQLApiClient {
  private config: MySQLConfig
  private connection: any = null

  constructor(config: MySQLConfig) {
    this.config = config
  }

  /**
   * 创建数据库连接
   */
  private async createConnection(): Promise<any> {
    // 尝试使用Tauri API连接MySQL
    try {
      const { invoke } = await import('@tauri-apps/api/core')

      return {
        query: async (sql: string, params?: any[]) => {
          const result = await invoke('mysql_query', {
            config: this.config,
            sql,
            params: params || []
          })
          // Tauri返回的是数组格式，需要包装成mysql2的格式
          return [result]
        },
        end: () => {
          // Tauri环境中连接由Rust管理
        }
      }
    } catch (error) {
      // 如果无法导入Tauri API，说明在纯浏览器环境中
      console.error('Tauri API不可用:', error)
      throw new Error('MySQL连接仅在Tauri桌面应用中支持，当前环境不支持数据库连接')
    }
  }

  /**
   * 获取数据库连接
   */
  private async getConnection(): Promise<any> {
    if (!this.connection) {
      this.connection = await this.createConnection()
    }
    return this.connection
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const connection = await this.getConnection()
      await connection.query('SELECT 1')
      return true
    } catch (error) {
      console.error('MySQL连接测试失败:', error)
      return false
    }
  }

  /**
   * 获取热门校招汇总表数据
   */
  async getHotAutumnData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 热门校招汇总')
      return this.convertMySQLRecords(rows)
    } catch (error) {
      console.error('获取热门校招汇总表数据失败:', error)
      throw error
    }
  }

  /**
   * 获取热门实习汇总表数据
   */
  async getHotInternshipData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 热门实习汇总')
      return this.convertMySQLRecords(rows)
    } catch (error) {
      console.error('获取热门实习汇总表数据失败:', error)
      throw error
    }
  }

  /**
   * 获取国企央企汇总表数据
   */
  async getStateOwnedData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 国企央企汇总')
      return this.convertMySQLRecords(rows)
    } catch (error) {
      console.error('获取国企央企汇总表数据失败:', error)
      throw error
    }
  }





  /**
   * 获取所有用户（从userdata表中提取）
   */
  async getAllUsers(): Promise<MySQLUser[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query(
        `SELECT DISTINCT username FROM userdata WHERE username IS NOT NULL AND username != '' ORDER BY username`
      )
      return rows.map((row: any, index: number) => ({
        id: index + 1,
        username: row.username,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    }
  }

  /**
   * 创建用户（在userdata表中插入记录）
   */
  async createUser(username: string): Promise<MySQLUser> {
    try {
      // 检查用户是否已存在
      const existingUsers = await this.getAllUsers()
      const existingUser = existingUsers.find(u => u.username === username)
      if (existingUser) {
        return existingUser
      }

      // 在userdata表中创建用户记录
      const connection = await this.getConnection()

      // 先测试简单的插入语句
      console.log('准备插入用户:', username)
      const sql = `INSERT INTO userdata (username, \`公司\`, \`投递链接\`, \`重视度\`, \`职位\`, \`地点\`, \`进展\`, \`状态\`, \`进展时间\`, \`投递时间\`, \`备注\`, \`内推码\`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
      const values = [username, '', '', 2, '', '', '', '', '', '', '', '']

      console.log('SQL:', sql)
      console.log('Values:', values)

      await connection.query(sql, values)

      // 返回用户信息
      return {
        id: Date.now(), // 临时ID
        username: username,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    } catch (error) {
      console.error('创建用户失败:', error)
      throw error
    }
  }

  /**
   * 获取用户的求职申请数据
   */
  async getUserJobApplications(username: string): Promise<MySQLJobApplication[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query(
        `SELECT * FROM userdata WHERE username = ? AND \`公司\` != "" ORDER BY id DESC`,
        [username]
      )
      return this.convertMySQLJobApplications(rows)
    } catch (error) {
      console.error('获取用户求职申请数据失败:', error)
      throw error
    }
  }

  /**
   * 保存求职申请数据
   */
  async saveJobApplication(application: Omit<MySQLJobApplication, 'id'>): Promise<MySQLJobApplication> {
    try {
      const connection = await this.getConnection()
      const [result] = await connection.query(
        `INSERT INTO userdata (username, \`公司\`, \`投递链接\`, \`重视度\`, \`职位\`, \`地点\`, \`进展\`, \`状态\`, \`进展时间\`, \`投递时间\`, \`备注\`, \`内推码\`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          application.username,
          application.公司,
          application.投递链接,
          application.重视度,
          application.职位,
          application.地点,
          application.进展,
          application.状态,
          application.进展时间,
          application.投递时间,
          application.备注,
          application.内推码
        ]
      )

      // 获取创建的记录
      const [rows] = await connection.query(
        'SELECT * FROM userdata WHERE id = ?',
        [result.insertId]
      )

      return this.convertMySQLJobApplications(rows)[0]
    } catch (error) {
      console.error('保存求职申请数据失败:', error)
      throw error
    }
  }

  /**
   * 批量保存求职申请数据
   */
  async batchSaveJobApplications(applications: Omit<MySQLJobApplication, 'id'>[]): Promise<void> {
    try {
      const connection = await this.getConnection()

      // 先清空该用户的数据（保留占位记录）
      if (applications.length > 0) {
        await connection.query(
          `DELETE FROM userdata WHERE username = ? AND \`公司\` != ""`,
          [applications[0].username]
        )
      }

      // 批量插入新数据
      if (applications.length > 0) {
        const values = applications.map(app => [
          app.username,
          app.公司,
          app.投递链接,
          app.重视度,
          app.职位,
          app.地点,
          app.进展,
          app.状态,
          app.进展时间,
          app.投递时间,
          app.备注,
          app.内推码
        ])

        await connection.query(
          `INSERT INTO userdata (username, \`公司\`, \`投递链接\`, \`重视度\`, \`职位\`, \`地点\`, \`进展\`, \`状态\`, \`进展时间\`, \`投递时间\`, \`备注\`, \`内推码\`) VALUES ?`,
          [values]
        )
      }

      console.log(`批量保存 ${applications.length} 条求职申请数据成功`)
    } catch (error) {
      console.error('批量保存求职申请数据失败:', error)
      throw error
    }
  }

  /**
   * 转换MySQL记录为标准格式
   */
  private convertMySQLRecords(rows: any[]): MySQLRecord[] {
    return rows.map((row, index) => ({
      id: row.id || Date.now() + index,
      updateTime: row.更新 || row.updateTime || '',
      company: row.公司 || row.company || '',
      applicationLink: row.投递链接 || row.applicationLink || '',
      industry: row.行业 || row.industry || '',
      tags: row.标签 || row.tags || '',
      batch: row.批次 || row.batch || '',
      isHot: String(row.热门 || row.isHot || ''),
      position: row.职位 || row.position || '',
      location: row.地点 || row.location || '',
      deadline: row.投递截至 || row.deadline || ''
    }))
  }

  /**
   * 转换MySQL用户记录
   */
  private convertMySQLUsers(rows: any[]): MySQLUser[] {
    return rows.map(row => ({
      id: row.id,
      username: row.username,
      created_at: row.created_at,
      updated_at: row.updated_at
    }))
  }

  /**
   * 转换MySQL求职申请记录
   */
  private convertMySQLJobApplications(rows: any[]): MySQLJobApplication[] {
    return rows.map(row => ({
      id: row.id,
      username: row.username,
      公司: row['公司'] || '',
      投递链接: row['投递链接'] || '',
      重视度: row['重视度'] || 2,
      职位: row['职位'] || '',
      地点: row['地点'] || '',
      进展: row['进展'] || '',
      状态: row['状态'] || '',
      进展时间: row['进展时间'] || '',
      投递时间: row['投递时间'] || '',
      备注: row['备注'] || '',
      内推码: row['内推码'] || ''
    }))
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    if (this.connection) {
      await this.connection.end()
      this.connection = null
    }
  }
}

/**
 * 创建MySQL API客户端实例
 */
export function createMySQLClient(config: MySQLConfig): MySQLApiClient {
  return new MySQLApiClient(config)
}

/**
 * 验证MySQL配置是否完整
 */
export function validateMySQLConfig(config: Partial<MySQLConfig>): config is MySQLConfig {
  return !!(config.host && config.port && config.user && config.password && config.database)
}

/**
 * 默认MySQL配置
 */
export const defaultMySQLConfig: MySQLConfig = {
  host: 'mysql5.sqlpub.com',
  port: 3310,
  user: 'fish2609',
  password: 'Hrk6OCOXYo39ToeS',
  database: 'jobfish_pool'
}

/**
 * 从localStorage加载MySQL配置
 */
export function loadMySQLConfigFromStorage(): MySQLConfig | null {
  try {
    const saved = localStorage.getItem('mysql-config')
    if (saved) {
      const config = JSON.parse(saved)
      if (validateMySQLConfig(config)) {
        return config
      }
    }
  } catch (error) {
    console.error('加载MySQL配置失败:', error)
  }
  return null
}

/**
 * 保存MySQL配置到localStorage
 */
export function saveMySQLConfigToStorage(config: MySQLConfig): void {
  try {
    localStorage.setItem('mysql-config', JSON.stringify(config))
  } catch (error) {
    console.error('保存MySQL配置失败:', error)
  }
}
