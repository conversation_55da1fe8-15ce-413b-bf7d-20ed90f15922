// MySQL数据库API服务
import { convertToMySQLFormat, convertFromMySQLFormat, MYSQL_FIELD_MAPPING } from '../types/jobApplication'
import type { JobApplication } from '../types/jobApplication'

export interface MySQLConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
}

export interface MySQLRecord {
  id?: number
  updateTime?: string
  company: string
  applicationLink: string
  industry: string
  tags: string
  batch: string
  isHot: string
  position: string
  location: string
  deadline: string
}

// 用户数据接口
export interface MySQLUser {
  id?: number
  username: string
  created_at?: string
  updated_at?: string
}

// MySQL原始数据接口（保持与数据库表结构一致）
export interface MySQLJobApplicationRaw {
  id?: number
  username: string
  公司: string
  投递链接: string
  重视度: number
  职位: string
  地点: string
  进展: string
  状态: string
  进展时间: string
  投递时间: string
  备注: string
  内推码: string
}

/**
 * MySQL数据库API客户端
 */
export class MySQLApiClient {
  private config: MySQLConfig
  private connection: any = null
  private isConnecting: boolean = false
  private connectionRetries: number = 0
  private maxRetries: number = 3

  constructor(config: MySQLConfig) {
    this.config = config
  }

  /**
   * 创建数据库连接
   */
  private async createConnection(): Promise<any> {
    // 尝试使用Tauri API连接MySQL
    try {
      const { invoke } = await import('@tauri-apps/api/core')

      return {
        query: async (sql: string, params?: any[]) => {
          try {
            const result = await invoke('mysql_query', {
              config: this.config,
              sql,
              params: params || []
            })
            // Tauri返回的是数组格式，需要包装成mysql2的格式
            return [result]
          } catch (error) {
            console.error('MySQL查询失败:', error)
            // 重置连接以便下次重新连接
            this.connection = null
            throw error
          }
        },
        end: () => {
          // Tauri环境中连接由Rust管理
          this.connection = null
        }
      }
    } catch (error) {
      // 如果无法导入Tauri API，说明在纯浏览器环境中
      console.error('Tauri API不可用:', error)
      throw new Error('MySQL连接仅在Tauri桌面应用中支持，当前环境不支持数据库连接')
    }
  }

  /**
   * 获取数据库连接（带重试机制）
   */
  private async getConnection(): Promise<any> {
    if (this.connection) {
      return this.connection
    }

    if (this.isConnecting) {
      // 等待当前连接完成
      while (this.isConnecting && this.connectionRetries < this.maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      if (this.connection) {
        return this.connection
      }
    }

    this.isConnecting = true

    try {
      this.connection = await this.createConnection()
      this.connectionRetries = 0
      console.log('MySQL连接创建成功')
      return this.connection
    } catch (error) {
      this.connectionRetries++
      console.error(`MySQL连接失败 (尝试 ${this.connectionRetries}/${this.maxRetries}):`, error)

      if (this.connectionRetries >= this.maxRetries) {
        throw new Error(`MySQL连接失败，已重试 ${this.maxRetries} 次: ${error}`)
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * this.connectionRetries))
      return this.getConnection()
    } finally {
      this.isConnecting = false
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const connection = await this.getConnection()
      await connection.query('SELECT 1')
      console.log('MySQL连接测试成功')
      return true
    } catch (error) {
      console.error('MySQL连接测试失败:', error)
      this.connection = null // 重置连接
      return false
    }
  }

  /**
   * 重置连接
   */
  async resetConnection(): Promise<void> {
    if (this.connection) {
      try {
        await this.connection.end()
      } catch (error) {
        console.warn('关闭旧连接时出错:', error)
      }
    }
    this.connection = null
    this.connectionRetries = 0
    this.isConnecting = false
  }

  /**
   * 获取热门校招汇总表数据
   */
  async getHotAutumnData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 热门校招汇总 ORDER BY id DESC')
      const records = this.convertMySQLRecords(rows)
      console.log(`获取热门校招汇总数据成功: ${records.length} 条记录`)
      return records
    } catch (error) {
      console.error('获取热门校招汇总表数据失败:', error)
      await this.resetConnection() // 重置连接
      throw new Error(`获取热门校招汇总表数据失败: ${error}`)
    }
  }

  /**
   * 获取热门实习汇总表数据
   */
  async getHotInternshipData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 热门实习汇总 ORDER BY id DESC')
      const records = this.convertMySQLRecords(rows)
      console.log(`获取热门实习汇总数据成功: ${records.length} 条记录`)
      return records
    } catch (error) {
      console.error('获取热门实习汇总表数据失败:', error)
      await this.resetConnection()
      throw new Error(`获取热门实习汇总表数据失败: ${error}`)
    }
  }

  /**
   * 获取国企央企汇总表数据
   */
  async getStateOwnedData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 国企央企汇总 ORDER BY id DESC')
      const records = this.convertMySQLRecords(rows)
      console.log(`获取国企央企汇总数据成功: ${records.length} 条记录`)
      return records
    } catch (error) {
      console.error('获取国企央企汇总表数据失败:', error)
      await this.resetConnection()
      throw new Error(`获取国企央企汇总表数据失败: ${error}`)
    }
  }





  /**
   * 获取所有用户（从userdata表中提取）
   */
  async getAllUsers(): Promise<MySQLUser[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query(
        `SELECT DISTINCT username FROM userdata WHERE username IS NOT NULL AND username != '' ORDER BY username`
      )
      return rows.map((row: any, index: number) => ({
        id: index + 1,
        username: row.username,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
    } catch (error) {
      console.error('获取用户列表失败:', error)
      await this.resetConnection()
      throw new Error(`获取用户列表失败: ${error}`)
    }
  }

  /**
   * 创建用户（直接在userdata表中插入一条新的求职申请记录）
   */
  async createUser(username: string): Promise<MySQLUser> {
    try {
      if (!username || username.trim() === '') {
        throw new Error('用户名不能为空')
      }

      const trimmedUsername = username.trim()
      const connection = await this.getConnection()

      // 直接插入一条空的求职申请记录作为用户创建
      const mysqlData = convertToMySQLFormat({
        username: trimmedUsername,
        company: '',
        applicationLink: '',
        priority: 2,
        industry: '',
        tags: '',
        position: '',
        location: '',
        progress: '',
        status: '',
        progressDate: '',
        applicationDate: '',
        notes: '',
        referralCode: ''
      })

      await connection.query(
        `INSERT INTO userdata (username, \`公司\`, \`投递链接\`, \`重视度\`, \`职位\`, \`地点\`, \`进展\`, \`状态\`, \`进展时间\`, \`投递时间\`, \`备注\`, \`内推码\`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mysqlData.username,
          mysqlData[MYSQL_FIELD_MAPPING.company],
          mysqlData[MYSQL_FIELD_MAPPING.applicationLink],
          mysqlData[MYSQL_FIELD_MAPPING.priority],
          mysqlData[MYSQL_FIELD_MAPPING.position],
          mysqlData[MYSQL_FIELD_MAPPING.location],
          mysqlData[MYSQL_FIELD_MAPPING.progress],
          mysqlData[MYSQL_FIELD_MAPPING.status],
          mysqlData[MYSQL_FIELD_MAPPING.progressDate],
          mysqlData[MYSQL_FIELD_MAPPING.applicationDate],
          mysqlData[MYSQL_FIELD_MAPPING.notes],
          mysqlData[MYSQL_FIELD_MAPPING.referralCode]
        ]
      )

      console.log(`用户 ${trimmedUsername} 创建成功（插入了一条空记录）`)

      return {
        id: Date.now(),
        username: trimmedUsername,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    } catch (error) {
      console.error('创建用户失败:', error)
      await this.resetConnection()
      throw new Error(`创建用户失败: ${error}`)
    }
  }

  /**
   * 删除用户（删除userdata表中该用户的所有记录）
   */
  async deleteUser(username: string): Promise<void> {
    try {
      if (!username || username.trim() === '') {
        throw new Error('用户名不能为空')
      }

      const connection = await this.getConnection()
      const [result] = await connection.query(
        'DELETE FROM userdata WHERE username = ?',
        [username.trim()]
      )

      console.log(`用户 ${username} 的所有数据已删除`)
    } catch (error) {
      console.error('删除用户失败:', error)
      await this.resetConnection()
      throw new Error(`删除用户失败: ${error}`)
    }
  }

  /**
   * 获取用户的求职申请数据
   */
  async getUserJobApplications(username: string): Promise<JobApplication[]> {
    try {
      if (!username || username.trim() === '') {
        throw new Error('用户名不能为空')
      }

      const connection = await this.getConnection()
      const [rows] = await connection.query(
        `SELECT * FROM userdata WHERE username = ? AND \`公司\` != "" ORDER BY id DESC`,
        [username.trim()]
      )

      const applications = this.convertMySQLJobApplications(rows)
      console.log(`获取用户 ${username} 的求职申请数据成功: ${applications.length} 条记录`)
      return applications
    } catch (error) {
      console.error('获取用户求职申请数据失败:', error)
      await this.resetConnection()
      throw new Error(`获取用户求职申请数据失败: ${error}`)
    }
  }

  /**
   * 保存求职申请数据
   */
  async saveJobApplication(application: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>): Promise<JobApplication> {
    try {
      const connection = await this.getConnection()
      const mysqlData = convertToMySQLFormat(application)

      const [result] = await connection.query(
        `INSERT INTO userdata (username, \`公司\`, \`投递链接\`, \`重视度\`, \`职位\`, \`地点\`, \`进展\`, \`状态\`, \`进展时间\`, \`投递时间\`, \`备注\`, \`内推码\`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mysqlData.username,
          mysqlData[MYSQL_FIELD_MAPPING.company],
          mysqlData[MYSQL_FIELD_MAPPING.applicationLink],
          mysqlData[MYSQL_FIELD_MAPPING.priority],
          mysqlData[MYSQL_FIELD_MAPPING.position],
          mysqlData[MYSQL_FIELD_MAPPING.location],
          mysqlData[MYSQL_FIELD_MAPPING.progress],
          mysqlData[MYSQL_FIELD_MAPPING.status],
          mysqlData[MYSQL_FIELD_MAPPING.progressDate],
          mysqlData[MYSQL_FIELD_MAPPING.applicationDate],
          mysqlData[MYSQL_FIELD_MAPPING.notes],
          mysqlData[MYSQL_FIELD_MAPPING.referralCode]
        ]
      )

      // 获取创建的记录
      const [rows] = await connection.query(
        'SELECT * FROM userdata WHERE id = ?',
        [result.insertId]
      )

      return this.convertMySQLJobApplications(rows)[0]
    } catch (error) {
      console.error('保存求职申请数据失败:', error)
      throw error
    }
  }

  /**
   * 批量保存求职申请数据
   */
  async batchSaveJobApplications(applications: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<void> {
    try {
      const connection = await this.getConnection()

      // 先清空该用户的数据（保留占位记录）
      if (applications.length > 0) {
        await connection.query(
          `DELETE FROM userdata WHERE username = ? AND \`公司\` != ""`,
          [applications[0].username]
        )
      }

      // 批量插入新数据
      if (applications.length > 0) {
        const values = applications.map(app => {
          const mysqlData = convertToMySQLFormat(app)
          return [
            mysqlData.username,
            mysqlData[MYSQL_FIELD_MAPPING.company],
            mysqlData[MYSQL_FIELD_MAPPING.applicationLink],
            mysqlData[MYSQL_FIELD_MAPPING.priority],
            mysqlData[MYSQL_FIELD_MAPPING.position],
            mysqlData[MYSQL_FIELD_MAPPING.location],
            mysqlData[MYSQL_FIELD_MAPPING.progress],
            mysqlData[MYSQL_FIELD_MAPPING.status],
            mysqlData[MYSQL_FIELD_MAPPING.progressDate],
            mysqlData[MYSQL_FIELD_MAPPING.applicationDate],
            mysqlData[MYSQL_FIELD_MAPPING.notes],
            mysqlData[MYSQL_FIELD_MAPPING.referralCode]
          ]
        })

        await connection.query(
          `INSERT INTO userdata (username, \`公司\`, \`投递链接\`, \`重视度\`, \`职位\`, \`地点\`, \`进展\`, \`状态\`, \`进展时间\`, \`投递时间\`, \`备注\`, \`内推码\`) VALUES ?`,
          [values]
        )
      }

      console.log(`批量保存 ${applications.length} 条求职申请数据成功`)
    } catch (error) {
      console.error('批量保存求职申请数据失败:', error)
      throw error
    }
  }

  /**
   * 更新求职申请数据
   */
  async updateJobApplication(id: number, updates: Partial<JobApplication>): Promise<JobApplication> {
    try {
      const connection = await this.getConnection()

      // 转换为MySQL格式
      const mysqlUpdates = convertToMySQLFormat(updates)

      // 构建更新语句
      const updateFields: string[] = []
      const updateValues: any[] = []

      Object.entries(mysqlUpdates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'username') {
          updateFields.push(`\`${key}\` = ?`)
          updateValues.push(value)
        }
      })

      if (updateFields.length === 0) {
        throw new Error('没有需要更新的字段')
      }

      updateValues.push(id)

      await connection.query(
        `UPDATE userdata SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      )

      // 获取更新后的记录
      const [rows] = await connection.query(
        'SELECT * FROM userdata WHERE id = ?',
        [id]
      )

      return this.convertMySQLJobApplications(rows)[0]
    } catch (error) {
      console.error('更新求职申请数据失败:', error)
      throw error
    }
  }

  /**
   * 转换MySQL记录为标准格式
   */
  private convertMySQLRecords(rows: any[]): MySQLRecord[] {
    return rows.map((row, index) => ({
      id: row.id || Date.now() + index,
      updateTime: row.更新 || row.updateTime || '',
      company: row.公司 || row.company || '',
      applicationLink: row.投递链接 || row.applicationLink || '',
      industry: row.行业 || row.industry || '',
      tags: row.标签 || row.tags || '',
      batch: row.批次 || row.batch || '',
      isHot: String(row.热门 || row.isHot || ''),
      position: row.职位 || row.position || '',
      location: row.地点 || row.location || '',
      deadline: row.投递截至 || row.deadline || ''
    }))
  }

  /**
   * 转换MySQL用户记录
   */
  private convertMySQLUsers(rows: any[]): MySQLUser[] {
    return rows.map(row => ({
      id: row.id,
      username: row.username,
      created_at: row.created_at,
      updated_at: row.updated_at
    }))
  }

  /**
   * 转换MySQL求职申请记录
   */
  private convertMySQLJobApplications(rows: any[]): JobApplication[] {
    return rows.map(row => {
      // 构建MySQL原始数据对象
      const mysqlRawData: any = {
        id: row.id,
        username: row.username
      }

      // 添加所有MySQL字段
      Object.values(MYSQL_FIELD_MAPPING).forEach(mysqlField => {
        mysqlRawData[mysqlField] = row[mysqlField] || ''
      })

      // 特殊处理数字字段
      mysqlRawData[MYSQL_FIELD_MAPPING.priority] = row[MYSQL_FIELD_MAPPING.priority] || 2

      // 使用转换函数转换为标准格式
      return convertFromMySQLFormat(mysqlRawData)
    })
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    try {
      if (this.connection) {
        await this.connection.end()
        console.log('MySQL连接已关闭')
      }
    } catch (error) {
      console.warn('关闭MySQL连接时出错:', error)
    } finally {
      this.connection = null
      this.connectionRetries = 0
      this.isConnecting = false
    }
  }
}

/**
 * 创建MySQL API客户端实例
 */
export function createMySQLClient(config: MySQLConfig): MySQLApiClient {
  return new MySQLApiClient(config)
}

/**
 * 验证MySQL配置是否完整
 */
export function validateMySQLConfig(config: Partial<MySQLConfig>): config is MySQLConfig {
  return !!(config.host && config.port && config.user && config.password && config.database)
}

/**
 * 默认MySQL配置
 */
export const defaultMySQLConfig: MySQLConfig = {
  host: 'mysql5.sqlpub.com',
  port: 3310,
  user: 'fish2609',
  password: 'Hrk6OCOXYo39ToeS',
  database: 'jobfish_pool'
}

/**
 * 从localStorage加载MySQL配置
 */
export function loadMySQLConfigFromStorage(): MySQLConfig | null {
  try {
    const saved = localStorage.getItem('mysql-config')
    if (saved) {
      const config = JSON.parse(saved)
      if (validateMySQLConfig(config)) {
        return config
      }
    }
  } catch (error) {
    console.error('加载MySQL配置失败:', error)
  }
  return null
}

/**
 * 保存MySQL配置到localStorage
 */
export function saveMySQLConfigToStorage(config: MySQLConfig): void {
  try {
    localStorage.setItem('mysql-config', JSON.stringify(config))
  } catch (error) {
    console.error('保存MySQL配置失败:', error)
  }
}
